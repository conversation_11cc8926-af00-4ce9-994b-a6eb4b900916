@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 245, 245, 245;
  --background-end-rgb: 255, 255, 255;
  --header-height: 60px;
  --header-height-scrolled: 56px;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
  text-size-adjust: 100%; /* Prevent text size adjustment on orientation change */
  -webkit-font-smoothing: antialiased; /* Smoother font rendering */
  -moz-osx-font-smoothing: grayscale;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  overflow-x: hidden; /* Prevent horizontal scroll */
  padding-top: var(--header-height); /* Account for fixed header */
  min-height: 100vh; /* Ensure full height */
  transition: padding-top 0.3s ease; /* Smooth transition when header size changes */
}

/* Custom scrollbar - thinner and more subtle */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #16a34a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #15803d;
}

/* Performance optimizations */
img, video {
  max-width: 100%;
  height: auto;
  display: block; /* Prevent layout shifts */
}

/* Responsive media handling */
.responsive-media {
  position: relative;
  height: 0;
  overflow: hidden;
}

.responsive-media iframe,
.responsive-media object,
.responsive-media embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.aspect-16-9 {
  padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
}

.aspect-4-3 {
  padding-bottom: 75%; /* 4:3 Aspect Ratio */
}

.aspect-1-1 {
  padding-bottom: 100%; /* 1:1 Aspect Ratio */
}

@layer base {
  h1 {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold font-display leading-tight;
  }
  h2 {
    @apply text-2xl sm:text-3xl md:text-4xl font-bold font-display leading-tight;
  }
  h3 {
    @apply text-xl sm:text-2xl md:text-3xl font-semibold font-display leading-tight;
  }
  h4 {
    @apply text-lg sm:text-xl md:text-2xl font-semibold font-display;
  }
  h5 {
    @apply text-base sm:text-lg font-medium font-display;
  }
  h6 {
    @apply text-sm sm:text-base font-medium font-display;
  }
  
  /* Add a subtle transition to all elements */
  * {
    @apply transition-colors duration-200;
  }
  
  /* Improve focus styles for accessibility */
  a:focus, button:focus, input:focus, select:focus, textarea:focus {
    @apply outline-none ring-2 ring-green-500 ring-offset-2;
  }
  
  /* Optimize font rendering */
  p, li, a, span, div {
    @apply antialiased;
  }
}

@layer components {
  /* Modern button styles with improved hover effects */
  .btn-primary {
    @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 active:translate-y-0 active:shadow-md;
  }

  .btn-secondary {
    @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 active:translate-y-0 active:shadow-md;
  }

  .btn-accent {
    @apply bg-black hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 active:translate-y-0 active:shadow-md;
  }

  .btn-outline {
    @apply border-2 border-gray-300 hover:border-green-500 hover:bg-gray-50 text-gray-800 hover:text-green-600 font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:-translate-y-1 active:translate-y-0;
  }
  
  /* Enhanced card styles with better hover effects */
  .card {
    @apply bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 will-change-auto;
  }
  
  /* Section styling with more responsive padding */
  .section {
    @apply py-12 sm:py-16 md:py-20 lg:py-24 px-4 sm:px-6 md:px-8;
  }
  
  /* Container with more responsive padding */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Glass effect for elements */
  .glass {
    @apply bg-white/70 backdrop-blur-md border border-white/20 shadow-lg;
  }
  
  /* Gradient text with better performance */
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-red-600 will-change-auto;
  }
  
  /* Image hover effects with better performance */
  .img-hover-zoom {
    @apply overflow-hidden;
  }
  
  .img-hover-zoom img {
    @apply transition-transform duration-500 ease-in-out hover:scale-110 will-change-transform;
  }
  
  /* Animated underline for links */
  .animated-underline {
    @apply relative;
  }
  
  .animated-underline::after {
    @apply content-[''] absolute w-0 h-0.5 bg-green-500 left-0 -bottom-1 transition-all duration-300;
  }
  
  .animated-underline:hover::after {
    @apply w-full;
  }
  
  /* New animation classes */
  .fade-in {
    @apply opacity-0 animate-[fadeIn_0.5s_ease-in-out_forwards];
  }
  
  .slide-up {
    @apply opacity-0 translate-y-8 animate-[slideUp_0.5s_ease-out_forwards];
  }
  
  .slide-in-left {
    @apply opacity-0 -translate-x-8 animate-[slideInLeft_0.5s_ease-out_forwards];
  }
  
  .slide-in-right {
    @apply opacity-0 translate-x-8 animate-[slideInRight_0.5s_ease-out_forwards];
  }
  
  .scale-in {
    @apply opacity-0 scale-95 animate-[scaleIn_0.5s_ease-out_forwards];
  }
  
  /* Responsive grid layouts */
  .grid-auto-fit {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  .grid-auto-fit-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }
  
  /* Responsive spacing utilities */
  .responsive-gap {
    @apply gap-4 sm:gap-6 md:gap-8 lg:gap-10;
  }
  
  .responsive-p {
    @apply p-4 sm:p-6 md:p-8 lg:p-10;
  }
  
  .responsive-m {
    @apply m-4 sm:m-6 md:m-8 lg:m-10;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Media query for adjusting header height on scroll */
@media (max-width: 640px) {
  body {
    --header-height: 56px;
    --header-height-scrolled: 50px;
  }
}

/* Optimize images for different screen sizes */
@media (max-width: 640px) {
  .optimize-image {
    content-visibility: auto; /* Improve rendering performance */
  }
}
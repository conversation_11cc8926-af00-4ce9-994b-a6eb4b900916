'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import { useCreatePlayer } from '@/lib/hooks'
import { CreatePlayerRequest } from '@/lib/types'

export default function NewPlayer() {
  const router = useRouter()
  const createPlayer = useCreatePlayer()
  
  const [formData, setFormData] = useState<CreatePlayerRequest>({
    name: '',
    position: '',
    jersey_number: undefined,
    date_of_birth: '',
    nationality: '',
    height: undefined,
    weight: undefined,
    bio: '',
    image_url: '',
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const positions = [
    'Goalkeeper',
    'Defender',
    'Midfielder',
    'Forward',
    'Winger',
    'Striker',
    'Centre-back',
    'Full-back',
    'Wing-back',
    'Defensive Midfielder',
    'Central Midfielder',
    'Attacking Midfielder',
  ]

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.position) {
      newErrors.position = 'Position is required'
    }

    if (formData.jersey_number && (formData.jersey_number < 1 || formData.jersey_number > 99)) {
      newErrors.jersey_number = 'Jersey number must be between 1 and 99'
    }

    if (formData.height && formData.height < 100) {
      newErrors.height = 'Height must be at least 100cm'
    }

    if (formData.weight && formData.weight < 30) {
      newErrors.weight = 'Weight must be at least 30kg'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const result = await createPlayer.mutate(formData)
    if (result) {
      router.push('/mwenye-kiti/players')
    }
  }

  const handleInputChange = (field: keyof CreatePlayerRequest, value: string | number | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="mb-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Link
            href="/mwenye-kiti/players"
            className="inline-flex items-center text-sm text-gray-500 hover:text-green-600 mb-4 transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Players
          </Link>
        </motion.div>
        <motion.h1 
          className="text-3xl font-bold bg-gradient-to-r from-green-600 to-red-600 bg-clip-text text-transparent"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Add New Player
        </motion.h1>
        <motion.p 
          className="mt-1 text-sm text-gray-600"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Fill in the information below to add a new player to your team.
        </motion.p>
      </div>

      {/* Form */}
      <motion.div 
        className="bg-white shadow-lg rounded-xl border border-gray-100"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <form onSubmit={handleSubmit} className="space-y-6 p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Name */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.name ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="Enter player name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </motion.div>

            {/* Position */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <label htmlFor="position" className="block text-sm font-medium text-gray-700">
                Position *
              </label>
              <select
                id="position"
                value={formData.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.position ? 'border-red-300 ring-red-500' : ''
                }`}
              >
                <option value="">Select position</option>
                {positions.map(position => (
                  <option key={position} value={position}>{position}</option>
                ))}
              </select>
              {errors.position && <p className="mt-1 text-sm text-red-600">{errors.position}</p>}
            </motion.div>

            {/* Jersey Number */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
            >
              <label htmlFor="jersey_number" className="block text-sm font-medium text-gray-700">
                Jersey Number
              </label>
              <input
                type="number"
                id="jersey_number"
                min="1"
                max="99"
                value={formData.jersey_number || ''}
                onChange={(e) => handleInputChange('jersey_number', e.target.value ? parseInt(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.jersey_number ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="e.g., 10"
              />
              {errors.jersey_number && <p className="mt-1 text-sm text-red-600">{errors.jersey_number}</p>}
            </motion.div>

            {/* Date of Birth */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.7 }}
            >
              <label htmlFor="date_of_birth" className="block text-sm font-medium text-gray-700">
                Date of Birth
              </label>
              <input
                type="date"
                id="date_of_birth"
                value={formData.date_of_birth}
                onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              />
            </motion.div>

            {/* Nationality */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.8 }}
            >
              <label htmlFor="nationality" className="block text-sm font-medium text-gray-700">
                Nationality
              </label>
              <input
                type="text"
                id="nationality"
                value={formData.nationality || ''}
                onChange={(e) => handleInputChange('nationality', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="e.g., Kenya"
              />
            </motion.div>

            {/* Height */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.9 }}
            >
              <label htmlFor="height" className="block text-sm font-medium text-gray-700">
                Height (cm)
              </label>
              <input
                type="number"
                id="height"
                min="100"
                max="250"
                value={formData.height || ''}
                onChange={(e) => handleInputChange('height', e.target.value ? parseFloat(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.height ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="e.g., 180"
              />
              {errors.height && <p className="mt-1 text-sm text-red-600">{errors.height}</p>}
            </motion.div>

            {/* Weight */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 1.0 }}
            >
              <label htmlFor="weight" className="block text-sm font-medium text-gray-700">
                Weight (kg)
              </label>
              <input
                type="number"
                id="weight"
                min="30"
                max="150"
                value={formData.weight || ''}
                onChange={(e) => handleInputChange('weight', e.target.value ? parseFloat(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.weight ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="e.g., 75"
              />
              {errors.weight && <p className="mt-1 text-sm text-red-600">{errors.weight}</p>}
            </motion.div>

            {/* Image URL */}
            <motion.div 
              className="sm:col-span-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 1.1 }}
            >
              <label htmlFor="image_url" className="block text-sm font-medium text-gray-700">
                Image URL
              </label>
              <input
                type="url"
                id="image_url"
                value={formData.image_url || ''}
                onChange={(e) => handleInputChange('image_url', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="https://example.com/player-image.jpg"
              />
            </motion.div>
          </div>

          {/* Bio */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.2 }}
          >
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
              Biography
            </label>
            <textarea
              id="bio"
              rows={4}
              value={formData.bio || ''}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              placeholder="Tell us about this player..."
            />
          </motion.div>

          {/* Error Message */}
          {createPlayer.error && (
            <motion.div 
              className="rounded-lg bg-red-50 border border-red-200 p-4"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              <div className="text-sm text-red-700">
                Error creating player: {createPlayer.error}
              </div>
            </motion.div>
          )}

          {/* Submit Buttons */}
          <motion.div 
            className="flex justify-end space-x-3"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.3 }}
          >
            <Link
              href="/mwenye-kiti/players"
              className="bg-white py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
            >
              Cancel
            </Link>
            <motion.button
              type="submit"
              disabled={createPlayer.loading}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 transition-all duration-200"
            >
              {createPlayer.loading ? 'Creating...' : 'Create Player'}
            </motion.button>
          </motion.div>
        </form>
      </motion.div>
    </motion.div>
  )
}

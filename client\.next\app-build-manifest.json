{"pages": {"/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/error": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/error.js"], "/loading": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/loading.js"], "/not-found": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/not-found.js"], "/_not-found/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/_not-found/page.js"], "/about/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/about/page.js"], "/team/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/team/page.js"], "/team/[id]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/team/[id]/page.js"], "/team/[id]/not-found": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/team/[id]/not-found.js"], "/matches/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/matches/page.js"], "/news/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/news/page.js"], "/gallery/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/gallery/page.js"], "/mwenye-kiti/login/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/mwenye-kiti/login/page.js"], "/mwenye-kiti/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/mwenye-kiti/layout.js"], "/mwenye-kiti/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/mwenye-kiti/page.js"]}}
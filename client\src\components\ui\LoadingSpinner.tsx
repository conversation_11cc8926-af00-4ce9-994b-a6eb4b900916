'use client'

import { motion } from 'framer-motion'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'green' | 'red' | 'black' | 'white'
  text?: string
}

export default function LoadingSpinner({ 
  size = 'md', 
  color = 'green', 
  text 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  const colorClasses = {
    green: 'border-green-200 border-t-green-600',
    red: 'border-red-200 border-t-red-600',
    black: 'border-gray-200 border-t-black',
    white: 'border-white/20 border-t-white'
  }

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg'
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-3">
      <motion.div
        className={`${sizeClasses[size]} border-4 ${colorClasses[color]} rounded-full`}
        animate={{ rotate: 360 }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: "linear"
        }}
      />
      {text && (
        <motion.p
          className={`${textSizeClasses[size]} text-gray-600 font-medium`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  )
}

// Pulse loading animation for skeleton screens
export function PulseLoader({ className = '' }: { className?: string }) {
  return (
    <motion.div
      className={`bg-gray-200 rounded ${className}`}
      animate={{
        opacity: [0.5, 1, 0.5]
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  )
}

// Dots loading animation
export function DotsLoader({ color = 'green' }: { color?: 'green' | 'red' | 'black' }) {
  const colorClasses = {
    green: 'bg-green-600',
    red: 'bg-red-600',
    black: 'bg-black'
  }

  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`w-2 h-2 ${colorClasses[color]} rounded-full`}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: index * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  )
}

// Wave loading animation
export function WaveLoader({ color = 'green' }: { color?: 'green' | 'red' | 'black' }) {
  const colorClasses = {
    green: 'bg-green-600',
    red: 'bg-red-600',
    black: 'bg-black'
  }

  return (
    <div className="flex items-end space-x-1">
      {[0, 1, 2, 3, 4].map((index) => (
        <motion.div
          key={index}
          className={`w-1 ${colorClasses[color]} rounded-full`}
          animate={{
            height: [8, 24, 8]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: index * 0.1,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  )
}

// Bouncing ball loader
export function BouncingBallLoader({ color = 'green' }: { color?: 'green' | 'red' | 'black' }) {
  const colorClasses = {
    green: 'bg-green-600',
    red: 'bg-red-600',
    black: 'bg-black'
  }

  return (
    <div className="flex space-x-2">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`w-3 h-3 ${colorClasses[color]} rounded-full`}
          animate={{
            y: [0, -20, 0]
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.1,
            ease: "easeOut"
          }}
        />
      ))}
    </div>
  )
}

// Spinning squares loader
export function SpinningSquaresLoader({ color = 'green' }: { color?: 'green' | 'red' | 'black' }) {
  const colorClasses = {
    green: 'bg-green-600',
    red: 'bg-red-600',
    black: 'bg-black'
  }

  return (
    <div className="grid grid-cols-2 gap-1 w-8 h-8">
      {[0, 1, 2, 3].map((index) => (
        <motion.div
          key={index}
          className={`w-3 h-3 ${colorClasses[color]} rounded-sm`}
          animate={{
            scale: [1, 0.8, 1],
            rotate: [0, 90, 180, 270, 360]
          }}
          transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: index * 0.1,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  )
}

// Progress bar loader
export function ProgressLoader({ 
  progress = 0, 
  color = 'green',
  showPercentage = false 
}: { 
  progress?: number
  color?: 'green' | 'red' | 'black'
  showPercentage?: boolean 
}) {
  const colorClasses = {
    green: 'bg-green-600',
    red: 'bg-red-600',
    black: 'bg-black'
  }

  return (
    <div className="w-full">
      <div className="w-full bg-gray-200 rounded-full h-2">
        <motion.div
          className={`h-2 ${colorClasses[color]} rounded-full`}
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>
      {showPercentage && (
        <motion.p
          className="text-sm text-gray-600 mt-2 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          {Math.round(progress)}%
        </motion.p>
      )}
    </div>
  )
}

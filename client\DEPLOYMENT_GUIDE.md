# Football Club Admin System - Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Football Club Admin System to production environments.

## Prerequisites

### System Requirements
- Node.js 18+ 
- npm or yarn package manager
- Git for version control
- SSL certificate for HTTPS
- Domain name configured

### Environment Setup
- Production server (VPS, cloud instance, etc.)
- Database server (if separate)
- CDN for static assets (optional)
- Monitoring tools (optional)

## Build Process

### 1. Prepare for Production

```bash
# Clone the repository
git clone <repository-url>
cd client

# Install dependencies
npm install

# Create production environment file
cp .env.example .env.production
```

### 2. Configure Environment Variables

Edit `.env.production`:

```env
# API Configuration
NEXT_PUBLIC_API_URL=https://your-api-domain.com/api

# Application Settings
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Security Settings
NEXTAUTH_SECRET=your-super-secret-key-here
NEXTAUTH_URL=https://your-domain.com

# Analytics (optional)
NEXT_PUBLIC_GA_ID=your-google-analytics-id

# Error Reporting (optional)
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
```

### 3. Build the Application

```bash
# Build for production
npm run build

# Test the production build locally
npm start
```

### 4. Optimize Build

```bash
# Analyze bundle size
npm run analyze

# Run production checks
npm run lint
npm run type-check
```

## Deployment Options

### Option 1: Vercel (Recommended)

Vercel provides the easiest deployment for Next.js applications.

#### Setup Steps:

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel --prod
   ```

4. **Configure Environment Variables**
   - Go to Vercel dashboard
   - Navigate to your project settings
   - Add environment variables from `.env.production`

5. **Custom Domain**
   - Add your custom domain in Vercel dashboard
   - Configure DNS records as instructed

#### Vercel Configuration

Create `vercel.json`:

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/**": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ]
}
```

### Option 2: Netlify

1. **Build Settings**
   - Build command: `npm run build`
   - Publish directory: `out` (if using static export) or `.next`

2. **Environment Variables**
   - Add all production environment variables in Netlify dashboard

3. **Redirects**
   Create `_redirects` file in `public/`:
   ```
   /mwenye-kiti/* /mwenye-kiti/index.html 200
   /* /index.html 200
   ```

### Option 3: Docker Deployment

#### Dockerfile

Create `Dockerfile`:

```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM node:18-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://your-api-domain.com/api
    restart: unless-stopped
    depends_on:
      - backend

  backend:
    # Your backend service configuration
    image: your-backend-image
    ports:
      - "8080:8080"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
    restart: unless-stopped
```

### Option 4: Traditional VPS/Server

#### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y
```

#### 2. Application Deployment

```bash
# Clone and build application
git clone <repository-url> /var/www/football-club
cd /var/www/football-club/client

# Install dependencies and build
npm install
npm run build

# Create PM2 ecosystem file
```

Create `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'football-club-frontend',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/football-club/client',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G',
    error_file: '/var/log/pm2/football-club-error.log',
    out_file: '/var/log/pm2/football-club-out.log',
    log_file: '/var/log/pm2/football-club.log'
  }]
}
```

#### 3. Start Application

```bash
# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
```

#### 4. Nginx Configuration

Create `/etc/nginx/sites-available/football-club`:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static assets caching
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }

    # API proxy (if needed)
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/football-club /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL Certificate Setup

### Using Let's Encrypt (Free)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### Using Custom Certificate

1. Upload certificate files to server
2. Update Nginx configuration with certificate paths
3. Test SSL configuration

## Monitoring and Maintenance

### 1. Application Monitoring

```bash
# PM2 monitoring
pm2 monit

# View logs
pm2 logs football-club-frontend

# Restart application
pm2 restart football-club-frontend
```

### 2. System Monitoring

Install monitoring tools:

```bash
# Install htop for system monitoring
sudo apt install htop

# Install fail2ban for security
sudo apt install fail2ban
```

### 3. Backup Strategy

```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/football-club"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup application files
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /var/www/football-club

# Backup Nginx configuration
cp /etc/nginx/sites-available/football-club $BACKUP_DIR/nginx_$DATE.conf

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 4. Update Process

```bash
# Update application
cd /var/www/football-club
git pull origin main
cd client
npm install
npm run build
pm2 restart football-club-frontend
```

## Performance Optimization

### 1. CDN Setup

Configure CDN for static assets:
- Images
- CSS files
- JavaScript files
- Fonts

### 2. Caching Strategy

```nginx
# Browser caching
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. Database Optimization

- Enable query caching
- Optimize database indexes
- Regular database maintenance

## Security Checklist

### Application Security
- [ ] Environment variables secured
- [ ] API endpoints protected
- [ ] Input validation implemented
- [ ] XSS protection enabled
- [ ] CSRF protection enabled

### Server Security
- [ ] Firewall configured
- [ ] SSH key authentication
- [ ] Regular security updates
- [ ] Fail2ban configured
- [ ] SSL/TLS properly configured

### Monitoring
- [ ] Error logging enabled
- [ ] Performance monitoring
- [ ] Security monitoring
- [ ] Backup verification
- [ ] Update notifications

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version
   - Verify environment variables
   - Check for missing dependencies

2. **Runtime Errors**
   - Check PM2 logs
   - Verify API connectivity
   - Check file permissions

3. **SSL Issues**
   - Verify certificate validity
   - Check Nginx configuration
   - Test SSL configuration

4. **Performance Issues**
   - Monitor server resources
   - Check database performance
   - Analyze network latency

### Log Locations

- Application logs: `/var/log/pm2/`
- Nginx logs: `/var/log/nginx/`
- System logs: `/var/log/syslog`

## Rollback Procedure

In case of deployment issues:

```bash
# Stop current application
pm2 stop football-club-frontend

# Restore from backup
cd /var/www
rm -rf football-club
tar -xzf /backups/football-club/app_YYYYMMDD_HHMMSS.tar.gz

# Restart application
pm2 start football-club-frontend
```

## Support and Maintenance

### Regular Tasks
- Monitor application performance
- Check error logs daily
- Update dependencies monthly
- Security patches as needed
- Backup verification weekly

### Emergency Contacts
- Development team
- Server administrator
- Domain registrar
- SSL certificate provider

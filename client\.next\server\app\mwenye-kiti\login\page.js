/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/mwenye-kiti/login/page";
exports.ids = ["app/mwenye-kiti/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmwenye-kiti%2Flogin%2Fpage&page=%2Fmwenye-kiti%2Flogin%2Fpage&appPaths=%2Fmwenye-kiti%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fmwenye-kiti%2Flogin%2Fpage.tsx&appDir=C%3A%5Cprojects%5Cofc%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cprojects%5Cofc%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmwenye-kiti%2Flogin%2Fpage&page=%2Fmwenye-kiti%2Flogin%2Fpage&appPaths=%2Fmwenye-kiti%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fmwenye-kiti%2Flogin%2Fpage.tsx&appDir=C%3A%5Cprojects%5Cofc%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cprojects%5Cofc%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'mwenye-kiti',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mwenye-kiti/login/page.tsx */ \"(rsc)/./src/app/mwenye-kiti/login/page.tsx\")), \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mwenye-kiti/layout.tsx */ \"(rsc)/./src/app/mwenye-kiti/layout.tsx\")), \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\")), \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/mwenye-kiti/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/mwenye-kiti/login/page\",\n        pathname: \"/mwenye-kiti/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmwenye-kiti%2Flogin%2Fpage&page=%2Fmwenye-kiti%2Flogin%2Fpage&appPaths=%2Fmwenye-kiti%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fmwenye-kiti%2Flogin%2Fpage.tsx&appDir=C%3A%5Cprojects%5Cofc%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cprojects%5Cofc%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q29mYyU1QyU1Q2NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQW1GIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vdGJhbGwtY2x1Yi13ZWJzaXRlLz9mMTY1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxccHJvamVjdHNcXFxcb2ZjXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(ssr)/./src/app/loading.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q29mYyU1QyU1Q2NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvYWRpbmcudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzSkFBcUYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb290YmFsbC1jbHViLXdlYnNpdGUvPzI3MDEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxwcm9qZWN0c1xcXFxvZmNcXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcbG9hZGluZy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cmwenye-kiti%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cmwenye-kiti%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mwenye-kiti/layout.tsx */ \"(ssr)/./src/app/mwenye-kiti/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q29mYyU1QyU1Q2NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q213ZW55ZS1raXRpJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQWlHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vdGJhbGwtY2x1Yi13ZWJzaXRlLz83ZThmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxccHJvamVjdHNcXFxcb2ZjXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXG13ZW55ZS1raXRpXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cmwenye-kiti%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cmwenye-kiti%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cmwenye-kiti%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mwenye-kiti/login/page.tsx */ \"(ssr)/./src/app/mwenye-kiti/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q29mYyU1QyU1Q2NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q213ZW55ZS1raXRpJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXNHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vdGJhbGwtY2x1Yi13ZWJzaXRlLz85ZGM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxccHJvamVjdHNcXFxcb2ZjXFxcXGNsaWVudFxcXFxzcmNcXFxcYXBwXFxcXG13ZW55ZS1raXRpXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cmwenye-kiti%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q29mYyU1QyU1Q2NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q25vdC1mb3VuZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUF1RiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb3RiYWxsLWNsdWItd2Vic2l0ZS8/ODNmMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXHByb2plY3RzXFxcXG9mY1xcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxub3QtZm91bmQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cprojects%5C%5Cofc%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Log the error to an error reporting service\n        console.error(error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4 py-24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-lg w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mx-auto w-32 h-32 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-red-100 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"absolute inset-0 flex items-center justify-center text-red-500\",\n                                initial: {\n                                    scale: 0.5,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300,\n                                    damping: 15,\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.3\n                    },\n                    children: \"Something went wrong!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                    className: \"text-gray-600 mb-8\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.4\n                    },\n                    children: \"We apologize for the inconvenience. Please try again or return to the homepage.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.5\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                            onClick: reset,\n                            className: \"btn-primary\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 400,\n                                damping: 17\n                            },\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 400,\n                                damping: 17\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"btn-outline inline-block\",\n                                children: \"Return Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    className: \"flex space-x-2 justify-center items-center\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            className: \"h-4 w-4 bg-primary-600 rounded-full\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.5,\n                                    1\n                                ],\n                                opacity: [\n                                    1,\n                                    0.5,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                times: [\n                                    0,\n                                    0.5,\n                                    1\n                                ]\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            className: \"h-4 w-4 bg-primary-600 rounded-full\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.5,\n                                    1\n                                ],\n                                opacity: [\n                                    1,\n                                    0.5,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                times: [\n                                    0,\n                                    0.5,\n                                    1\n                                ],\n                                delay: 0.2\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            className: \"h-4 w-4 bg-primary-600 rounded-full\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.5,\n                                    1\n                                ],\n                                opacity: [\n                                    1,\n                                    0.5,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                times: [\n                                    0,\n                                    0.5,\n                                    1\n                                ],\n                                delay: 0.4\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                    className: \"mt-4 text-gray-700 font-medium\",\n                    initial: {\n                        opacity: 0,\n                        y: 10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.3\n                    },\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/mwenye-kiti/layout.tsx":
/*!****************************************!*\
  !*** ./src/app/mwenye-kiti/layout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone-outgoing.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Cog,Home,LogOut,Menu,Newspaper,PhoneOutgoing,Users,Users2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default,useAuth auto */ \n\n\n\n\n\n// Mock authentication - in a real app, this would be handled by a proper auth system\nconst useAuth = ()=>{\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is authenticated (mock implementation)\n        const token = localStorage.getItem(\"admin_token\");\n        setIsAuthenticated(!!token);\n        setLoading(false);\n        if (!token) {\n            router.push(\"/mwenye-kiti/login\");\n        }\n    }, [\n        router\n    ]);\n    const login = (username, password)=>{\n        // Mock login - in a real app, this would make an API call\n        if (username === \"admin\" && password === \"password\") {\n            localStorage.setItem(\"admin_token\", \"mock_token\");\n            setIsAuthenticated(true);\n            router.push(\"/mwenye-kiti\");\n            return true;\n        }\n        return false;\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"admin_token\");\n        setIsAuthenticated(false);\n        router.push(\"/mwenye-kiti/login\");\n    };\n    return {\n        isAuthenticated,\n        loading,\n        login,\n        logout\n    };\n};\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/mwenye-kiti\",\n        icon: _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Players\",\n        href: \"/mwenye-kiti/players\",\n        icon: _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"Matches\",\n        href: \"/mwenye-kiti/matches\",\n        icon: _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"News\",\n        href: \"/mwenye-kiti/news\",\n        icon: _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Gallery\",\n        href: \"/mwenye-kiti/gallery\",\n        icon: _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Team\",\n        href: \"/mwenye-kiti/team\",\n        icon: _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Coaches\",\n        href: \"/mwenye-kiti/coaches\",\n        icon: _barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction classNames(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\nfunction AdminLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { isAuthenticated, loading, logout } = useAuth();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-red-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                animate: {\n                    rotate: 360\n                },\n                transition: {\n                    duration: 1,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                },\n                className: \"rounded-full h-12 w-12 border-4 border-green-200 border-t-green-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null // Will redirect to login\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex overflow-hidden bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                children: sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    className: \"fixed inset-0 flex z-40 md:hidden\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                            onClick: ()=>setSidebarOpen(false),\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl\",\n                            initial: {\n                                x: -300\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: -300\n                            },\n                            transition: {\n                                type: \"spring\",\n                                damping: 25,\n                                stiffness: 200\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                        onClick: ()=>setSidebarOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 h-0 pt-5 pb-4 overflow-y-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 flex items-center px-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold bg-gradient-to-r from-green-600 to-red-600 bg-clip-text text-transparent\",\n                                                children: \"Mwenye Kiti\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                            className: \"mt-5 px-2 space-y-1\",\n                                            children: navigation.map((item)=>{\n                                                const isActive = pathname === item.href;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: item.href,\n                                                    className: classNames(isActive ? \"bg-green-100 text-green-900 border-r-4 border-green-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\", \"group flex items-center px-2 py-2 text-base font-medium rounded-md transition-all duration-200\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            className: classNames(isActive ? \"text-green-500\" : \"text-gray-400 group-hover:text-gray-500\", \"mr-4 h-6 w-6\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.name\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex border-t border-gray-200 p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: logout,\n                                        className: \"flex items-center text-gray-600 hover:text-red-600 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-3 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Logout\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        className: \"flex flex-col h-0 flex-1 border-r border-gray-200 bg-white shadow-lg\",\n                        initial: {\n                            x: -100,\n                            opacity: 0\n                        },\n                        animate: {\n                            x: 0,\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center flex-shrink-0 px-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold bg-gradient-to-r from-green-600 to-red-600 bg-clip-text text-transparent\",\n                                            children: \"Mwenye Kiti\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"mt-5 flex-1 px-2 bg-white space-y-1\",\n                                        children: navigation.map((item, index)=>{\n                                            const isActive = pathname === item.href;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.3,\n                                                    delay: index * 0.1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: item.href,\n                                                    className: classNames(isActive ? \"bg-green-100 text-green-900 border-r-4 border-green-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\", \"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 hover:scale-105\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            className: classNames(isActive ? \"text-green-500\" : \"text-gray-400 group-hover:text-gray-500\", \"mr-3 h-5 w-5\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, item.name, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 flex border-t border-gray-200 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    className: \"flex items-center text-gray-600 hover:text-red-600 transition-colors duration-200 hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-3 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Logout\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-0 flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500 transition-colors duration-200\",\n                            onClick: ()=>setSidebarOpen(true),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Cog_Home_LogOut_Menu_Newspaper_PhoneOutgoing_Users_Users2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 relative z-0 overflow-y-auto focus:outline-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            className: \"py-6\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\layout.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n// Export the useAuth hook for use in other components\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mwenye-kiti/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/mwenye-kiti/login/page.tsx":
/*!********************************************!*\
  !*** ./src/app/mwenye-kiti/login/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLogin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AdminLogin() {\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if already authenticated\n        const token = localStorage.getItem(\"admin_token\");\n        if (token) {\n            router.push(\"/mwenye-kiti\");\n        }\n    }, [\n        router\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        // Mock authentication - in a real app, this would make an API call\n        try {\n            if (username === \"admin\" && password === \"password\") {\n                localStorage.setItem(\"admin_token\", \"mock_token\");\n                router.push(\"/mwenye-kiti\");\n            } else {\n                setError(\"Invalid username or password\");\n            }\n        } catch (err) {\n            setError(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-red-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.6\n            },\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-green-600 shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-8 w-8 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Admin Access\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: \"Sign in to manage the football club\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.form, {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.4\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"username\",\n                                            name: \"username\",\n                                            type: \"text\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-4 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm transition-all duration-200\",\n                                            placeholder: \"Enter username\",\n                                            value: username,\n                                            onChange: (e)=>setUsername(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"relative\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: showPassword ? \"text\" : \"password\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-4 py-3 pr-12 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm transition-all duration-200\",\n                                            placeholder: \"Enter password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"absolute inset-y-0 right-0 top-6 pr-3 flex items-center hover:text-green-600 transition-colors\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"rounded-lg bg-red-50 border border-red-200 p-4\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.95\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-red-400\",\n                                            viewBox: \"0 0 20 20\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.7\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Signing in...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this) : \"Sign in\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"text-center\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.8\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600 bg-gray-50 p-4 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"text-gray-800\",\n                                        children: \"Demo Credentials:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 75\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono\",\n                                        children: \"Username: admin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 65\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono\",\n                                        children: \"Password: password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\mwenye-kiti\\\\login\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mwenye-kiti/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center px-4 py-24 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-lg w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.8\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.5,\n                        type: \"spring\",\n                        stiffness: 200\n                    },\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-9xl font-bold text-primary-500 opacity-20\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"relative -mt-20 text-6xl font-bold text-gray-800\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -10,\n                                    0\n                                ],\n                                rotate: [\n                                    0,\n                                    2,\n                                    0,\n                                    -2,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                repeat: Infinity,\n                                repeatType: \"mirror\",\n                                duration: 5,\n                                ease: \"easeInOut\"\n                            },\n                            children: \"Oops!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.2\n                    },\n                    children: \"Page Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                    className: \"text-gray-600 mb-8\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.3\n                    },\n                    children: \"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.4\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.98\n                        },\n                        transition: {\n                            type: \"spring\",\n                            stiffness: 400,\n                            damping: 17\n                        },\n                        className: \"inline-block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"btn-primary\",\n                            children: \"Return to Homepage\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mt-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute -top-10 -right-10 w-20 h-20 rounded-full bg-primary-100\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ],\n                                opacity: [\n                                    0.3,\n                                    0.5,\n                                    0.3\n                                ]\n                            },\n                            transition: {\n                                repeat: Infinity,\n                                duration: 4,\n                                ease: \"easeInOut\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute -bottom-5 -left-5 w-16 h-16 rounded-full bg-secondary-100\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.3,\n                                    1\n                                ],\n                                opacity: [\n                                    0.2,\n                                    0.4,\n                                    0.2\n                                ]\n                            },\n                            transition: {\n                                repeat: Infinity,\n                                duration: 5,\n                                ease: \"easeInOut\",\n                                delay: 1\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-10 w-10 mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: \"https://placehold.co/200x200/0284c7/FFFFFF/png?text=FC\",\n                                                alt: \"Football Club Logo\",\n                                                fill: true,\n                                                className: \"rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-white font-display\",\n                                            children: \"Football Club\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"Dedicated to excellence in football since 2010. Building champions on and off the field.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Facebook\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 30,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 29,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Instagram\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 36,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-400 hover:text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"YouTube\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-6 w-6\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/team\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Our Team\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/matches\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Matches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/news\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"News\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/gallery\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Photo Gallery\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact#join\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Join the Club\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-gray-400 hover:text-white\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Contact Info\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-2 mt-0.5 text-primary-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"123 Stadium Road, Sportsville, SP1 2FC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-2 mt-0.5 text-primary-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-5 w-5 mr-2 mt-0.5 text-primary-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+1 (123) 456-7890\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-12 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-gray-400\",\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" Football Club. All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeaderWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _header_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./header/index */ \"(ssr)/./src/components/layout/header/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction HeaderWrapper() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_index__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUVtQztBQUVwQixTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QscURBQU1BOzs7OztBQUNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb3RiYWxsLWNsdWItd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9IZWFkZXIudHN4PzA2OGIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgSGVhZGVyIGZyb20gJy4vaGVhZGVyL2luZGV4J1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSGVhZGVyV3JhcHBlcigpIHtcclxuICByZXR1cm4gPEhlYWRlciAvPlxyXG59Il0sIm5hbWVzIjpbIkhlYWRlciIsIkhlYWRlcldyYXBwZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/anim.ts":
/*!**********************************************!*\
  !*** ./src/components/layout/header/anim.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   background: () => (/* binding */ background),\n/* harmony export */   blur: () => (/* binding */ blur),\n/* harmony export */   fadeUp: () => (/* binding */ fadeUp),\n/* harmony export */   height: () => (/* binding */ height),\n/* harmony export */   opacity: () => (/* binding */ opacity),\n/* harmony export */   scaleUp: () => (/* binding */ scaleUp),\n/* harmony export */   slideIn: () => (/* binding */ slideIn),\n/* harmony export */   staggerContainer: () => (/* binding */ staggerContainer),\n/* harmony export */   translate: () => (/* binding */ translate)\n/* harmony export */ });\n// Optimized transitions for better performance\nconst fastTransition = {\n    duration: 0.3,\n    ease: [\n        0.25,\n        0.1,\n        0.25,\n        1.0\n    ] // Optimized cubic-bezier\n};\nconst smoothTransition = {\n    duration: 0.6,\n    ease: [\n        0.65,\n        0,\n        0.35,\n        1\n    ] // Optimized cubic-bezier for smoother motion\n};\n// Staggered children animation helper\nconst staggerContainer = {\n    hidden: {\n        opacity: 1\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.07,\n            delayChildren: 0.2\n        }\n    }\n};\n// Fade animation with optimized performance\nconst opacity = {\n    initial: {\n        opacity: 0\n    },\n    open: {\n        opacity: 1,\n        transition: fastTransition\n    },\n    closed: {\n        opacity: 0,\n        transition: fastTransition\n    }\n};\n// Background animation with hardware acceleration hints\nconst background = {\n    initial: {\n        height: 0,\n        willChange: \"height\" // Performance hint for browsers\n    },\n    open: {\n        height: \"100vh\",\n        transition: smoothTransition\n    },\n    closed: {\n        height: 0,\n        transition: smoothTransition\n    }\n};\n// Height animation with optimized performance\nconst height = {\n    initial: {\n        height: 0,\n        willChange: \"height\" // Performance hint\n    },\n    enter: {\n        height: \"auto\",\n        transition: smoothTransition\n    },\n    exit: {\n        height: 0,\n        transition: smoothTransition\n    }\n};\n// Translate animation with hardware acceleration\nconst translate = {\n    initial: {\n        y: \"100%\",\n        opacity: 0,\n        willChange: \"transform, opacity\" // Performance hint\n    },\n    enter: (i)=>({\n            y: 0,\n            opacity: 1,\n            transition: {\n                duration: 0.7,\n                ease: [\n                    0.65,\n                    0,\n                    0.35,\n                    1\n                ],\n                delay: i[0] * 0.1 // Shorter delays for better UX\n            }\n        }),\n    exit: (i)=>({\n            y: \"100%\",\n            opacity: 0,\n            transition: {\n                duration: 0.5,\n                ease: [\n                    0.65,\n                    0,\n                    0.35,\n                    1\n                ],\n                delay: i[1] * 0.05 // Shorter delays for better UX\n            }\n        })\n};\n// Blur animation (use sparingly as it can be performance-heavy)\nconst blur = {\n    initial: {\n        filter: \"blur(0px)\",\n        opacity: 1\n    },\n    open: {\n        filter: \"blur(4px)\",\n        opacity: 0.6,\n        transition: fastTransition\n    },\n    closed: {\n        filter: \"blur(0px)\",\n        opacity: 1,\n        transition: fastTransition\n    }\n};\n// New animations for enhanced UI\n// Fade up animation for content blocks\nconst fadeUp = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            ...fastTransition\n        }\n    }\n};\n// Scale animation for buttons and interactive elements\nconst scaleUp = {\n    initial: {\n        scale: 0.95,\n        opacity: 0\n    },\n    animate: {\n        scale: 1,\n        opacity: 1,\n        transition: {\n            type: \"spring\",\n            stiffness: 400,\n            damping: 17\n        }\n    },\n    tap: {\n        scale: 0.97\n    }\n};\n// Slide in from side animation\nconst slideIn = (direction = \"left\")=>({\n        hidden: {\n            x: direction === \"left\" ? -60 : 60,\n            opacity: 0\n        },\n        visible: {\n            x: 0,\n            opacity: 1,\n            transition: smoothTransition\n        }\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL2FuaW0udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRUEsK0NBQStDO0FBQy9DLE1BQU1BLGlCQUFpQjtJQUNyQkMsVUFBVTtJQUNWQyxNQUFNO1FBQUM7UUFBTTtRQUFLO1FBQU07S0FBSSxDQUFDLHlCQUF5QjtBQUN4RDtBQUVBLE1BQU1DLG1CQUFtQjtJQUN2QkYsVUFBVTtJQUNWQyxNQUFNO1FBQUM7UUFBTTtRQUFHO1FBQU07S0FBRSxDQUFDLDZDQUE2QztBQUN4RTtBQUVBLHNDQUFzQztBQUMvQixNQUFNRSxtQkFBNkI7SUFDeENDLFFBQVE7UUFBRUMsU0FBUztJQUFFO0lBQ3JCQyxTQUFTO1FBQ1BELFNBQVM7UUFDVEUsWUFBWTtZQUNWQyxpQkFBaUI7WUFDakJDLGVBQWU7UUFDakI7SUFDRjtBQUNGLEVBQUU7QUFFRiw0Q0FBNEM7QUFDckMsTUFBTUosVUFBb0I7SUFDL0JLLFNBQVM7UUFDUEwsU0FBUztJQUNYO0lBQ0FNLE1BQU07UUFDSk4sU0FBUztRQUNURSxZQUFZUjtJQUNkO0lBQ0FhLFFBQVE7UUFDTlAsU0FBUztRQUNURSxZQUFZUjtJQUNkO0FBQ0YsRUFBRTtBQUVGLHdEQUF3RDtBQUNqRCxNQUFNYyxhQUF1QjtJQUNsQ0gsU0FBUztRQUNQSSxRQUFRO1FBQ1JDLFlBQVksU0FBUyxnQ0FBZ0M7SUFDdkQ7SUFDQUosTUFBTTtRQUNKRyxRQUFRO1FBQ1JQLFlBQVlMO0lBQ2Q7SUFDQVUsUUFBUTtRQUNORSxRQUFRO1FBQ1JQLFlBQVlMO0lBQ2Q7QUFDRixFQUFFO0FBRUYsOENBQThDO0FBQ3ZDLE1BQU1ZLFNBQW1CO0lBQzlCSixTQUFTO1FBQ1BJLFFBQVE7UUFDUkMsWUFBWSxTQUFTLG1CQUFtQjtJQUMxQztJQUNBQyxPQUFPO1FBQ0xGLFFBQVE7UUFDUlAsWUFBWUw7SUFDZDtJQUNBZSxNQUFNO1FBQ0pILFFBQVE7UUFDUlAsWUFBWUw7SUFDZDtBQUNGLEVBQUU7QUFFRixpREFBaUQ7QUFDMUMsTUFBTWdCLFlBQXNCO0lBQ2pDUixTQUFTO1FBQ1BTLEdBQUc7UUFDSGQsU0FBUztRQUNUVSxZQUFZLHFCQUFxQixtQkFBbUI7SUFDdEQ7SUFDQUMsT0FBTyxDQUFDSSxJQUFpQjtZQUN2QkQsR0FBRztZQUNIZCxTQUFTO1lBQ1RFLFlBQVk7Z0JBQ1ZQLFVBQVU7Z0JBQ1ZDLE1BQU07b0JBQUM7b0JBQU07b0JBQUc7b0JBQU07aUJBQUU7Z0JBQ3hCb0IsT0FBT0QsQ0FBQyxDQUFDLEVBQUUsR0FBRyxJQUFJLCtCQUErQjtZQUNuRDtRQUNGO0lBQ0FILE1BQU0sQ0FBQ0csSUFBaUI7WUFDdEJELEdBQUc7WUFDSGQsU0FBUztZQUNURSxZQUFZO2dCQUNWUCxVQUFVO2dCQUNWQyxNQUFNO29CQUFDO29CQUFNO29CQUFHO29CQUFNO2lCQUFFO2dCQUN4Qm9CLE9BQU9ELENBQUMsQ0FBQyxFQUFFLEdBQUcsS0FBSywrQkFBK0I7WUFDcEQ7UUFDRjtBQUNGLEVBQUU7QUFFRixnRUFBZ0U7QUFDekQsTUFBTUUsT0FBaUI7SUFDNUJaLFNBQVM7UUFDUGEsUUFBUTtRQUNSbEIsU0FBUztJQUNYO0lBQ0FNLE1BQU07UUFDSlksUUFBUTtRQUNSbEIsU0FBUztRQUNURSxZQUFZUjtJQUNkO0lBQ0FhLFFBQVE7UUFDTlcsUUFBUTtRQUNSbEIsU0FBUztRQUNURSxZQUFZUjtJQUNkO0FBQ0YsRUFBRTtBQUVGLGlDQUFpQztBQUVqQyx1Q0FBdUM7QUFDaEMsTUFBTXlCLFNBQW1CO0lBQzlCcEIsUUFBUTtRQUFFQyxTQUFTO1FBQUdjLEdBQUc7SUFBRztJQUM1QmIsU0FBUztRQUNQRCxTQUFTO1FBQ1RjLEdBQUc7UUFDSFosWUFBWTtZQUFFLEdBQUdSLGNBQWM7UUFBQztJQUNsQztBQUNGLEVBQUU7QUFFRix1REFBdUQ7QUFDaEQsTUFBTTBCLFVBQW9CO0lBQy9CZixTQUFTO1FBQUVnQixPQUFPO1FBQU1yQixTQUFTO0lBQUU7SUFDbkNzQixTQUFTO1FBQ1BELE9BQU87UUFDUHJCLFNBQVM7UUFDVEUsWUFBWTtZQUNWcUIsTUFBTTtZQUNOQyxXQUFXO1lBQ1hDLFNBQVM7UUFDWDtJQUNGO0lBQ0FDLEtBQUs7UUFBRUwsT0FBTztJQUFLO0FBQ3JCLEVBQUU7QUFFRiwrQkFBK0I7QUFDeEIsTUFBTU0sVUFBVSxDQUFDQyxZQUE4QixNQUFNLEdBQWdCO1FBQzFFN0IsUUFBUTtZQUNOOEIsR0FBR0QsY0FBYyxTQUFTLENBQUMsS0FBSztZQUNoQzVCLFNBQVM7UUFDWDtRQUNBQyxTQUFTO1lBQ1A0QixHQUFHO1lBQ0g3QixTQUFTO1lBQ1RFLFlBQVlMO1FBQ2Q7SUFDRixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vdGJhbGwtY2x1Yi13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L2hlYWRlci9hbmltLnRzP2Y1MTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVmFyaWFudHMgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xyXG5cclxuLy8gT3B0aW1pemVkIHRyYW5zaXRpb25zIGZvciBiZXR0ZXIgcGVyZm9ybWFuY2VcclxuY29uc3QgZmFzdFRyYW5zaXRpb24gPSB7IFxyXG4gIGR1cmF0aW9uOiAwLjMsIFxyXG4gIGVhc2U6IFswLjI1LCAwLjEsIDAuMjUsIDEuMF0gLy8gT3B0aW1pemVkIGN1YmljLWJlemllclxyXG59O1xyXG5cclxuY29uc3Qgc21vb3RoVHJhbnNpdGlvbiA9IHsgXHJcbiAgZHVyYXRpb246IDAuNiwgXHJcbiAgZWFzZTogWzAuNjUsIDAsIDAuMzUsIDFdIC8vIE9wdGltaXplZCBjdWJpYy1iZXppZXIgZm9yIHNtb290aGVyIG1vdGlvblxyXG59O1xyXG5cclxuLy8gU3RhZ2dlcmVkIGNoaWxkcmVuIGFuaW1hdGlvbiBoZWxwZXJcclxuZXhwb3J0IGNvbnN0IHN0YWdnZXJDb250YWluZXI6IFZhcmlhbnRzID0ge1xyXG4gIGhpZGRlbjogeyBvcGFjaXR5OiAxIH0sXHJcbiAgdmlzaWJsZToge1xyXG4gICAgb3BhY2l0eTogMSxcclxuICAgIHRyYW5zaXRpb246IHtcclxuICAgICAgc3RhZ2dlckNoaWxkcmVuOiAwLjA3LFxyXG4gICAgICBkZWxheUNoaWxkcmVuOiAwLjJcclxuICAgIH1cclxuICB9XHJcbn07XHJcblxyXG4vLyBGYWRlIGFuaW1hdGlvbiB3aXRoIG9wdGltaXplZCBwZXJmb3JtYW5jZVxyXG5leHBvcnQgY29uc3Qgb3BhY2l0eTogVmFyaWFudHMgPSB7XHJcbiAgaW5pdGlhbDoge1xyXG4gICAgb3BhY2l0eTogMFxyXG4gIH0sXHJcbiAgb3Blbjoge1xyXG4gICAgb3BhY2l0eTogMSxcclxuICAgIHRyYW5zaXRpb246IGZhc3RUcmFuc2l0aW9uXHJcbiAgfSxcclxuICBjbG9zZWQ6IHtcclxuICAgIG9wYWNpdHk6IDAsXHJcbiAgICB0cmFuc2l0aW9uOiBmYXN0VHJhbnNpdGlvblxyXG4gIH1cclxufTtcclxuXHJcbi8vIEJhY2tncm91bmQgYW5pbWF0aW9uIHdpdGggaGFyZHdhcmUgYWNjZWxlcmF0aW9uIGhpbnRzXHJcbmV4cG9ydCBjb25zdCBiYWNrZ3JvdW5kOiBWYXJpYW50cyA9IHtcclxuICBpbml0aWFsOiB7XHJcbiAgICBoZWlnaHQ6IDAsXHJcbiAgICB3aWxsQ2hhbmdlOiBcImhlaWdodFwiIC8vIFBlcmZvcm1hbmNlIGhpbnQgZm9yIGJyb3dzZXJzXHJcbiAgfSxcclxuICBvcGVuOiB7XHJcbiAgICBoZWlnaHQ6ICcxMDB2aCcsXHJcbiAgICB0cmFuc2l0aW9uOiBzbW9vdGhUcmFuc2l0aW9uXHJcbiAgfSxcclxuICBjbG9zZWQ6IHtcclxuICAgIGhlaWdodDogMCxcclxuICAgIHRyYW5zaXRpb246IHNtb290aFRyYW5zaXRpb25cclxuICB9XHJcbn07XHJcblxyXG4vLyBIZWlnaHQgYW5pbWF0aW9uIHdpdGggb3B0aW1pemVkIHBlcmZvcm1hbmNlXHJcbmV4cG9ydCBjb25zdCBoZWlnaHQ6IFZhcmlhbnRzID0ge1xyXG4gIGluaXRpYWw6IHtcclxuICAgIGhlaWdodDogMCxcclxuICAgIHdpbGxDaGFuZ2U6IFwiaGVpZ2h0XCIgLy8gUGVyZm9ybWFuY2UgaGludFxyXG4gIH0sXHJcbiAgZW50ZXI6IHtcclxuICAgIGhlaWdodDogJ2F1dG8nLFxyXG4gICAgdHJhbnNpdGlvbjogc21vb3RoVHJhbnNpdGlvblxyXG4gIH0sXHJcbiAgZXhpdDoge1xyXG4gICAgaGVpZ2h0OiAwLFxyXG4gICAgdHJhbnNpdGlvbjogc21vb3RoVHJhbnNpdGlvblxyXG4gIH1cclxufTtcclxuXHJcbi8vIFRyYW5zbGF0ZSBhbmltYXRpb24gd2l0aCBoYXJkd2FyZSBhY2NlbGVyYXRpb25cclxuZXhwb3J0IGNvbnN0IHRyYW5zbGF0ZTogVmFyaWFudHMgPSB7XHJcbiAgaW5pdGlhbDoge1xyXG4gICAgeTogXCIxMDAlXCIsXHJcbiAgICBvcGFjaXR5OiAwLFxyXG4gICAgd2lsbENoYW5nZTogXCJ0cmFuc2Zvcm0sIG9wYWNpdHlcIiAvLyBQZXJmb3JtYW5jZSBoaW50XHJcbiAgfSxcclxuICBlbnRlcjogKGk6IG51bWJlcltdKSA9PiAoe1xyXG4gICAgeTogMCxcclxuICAgIG9wYWNpdHk6IDEsXHJcbiAgICB0cmFuc2l0aW9uOiB7IFxyXG4gICAgICBkdXJhdGlvbjogMC43LCBcclxuICAgICAgZWFzZTogWzAuNjUsIDAsIDAuMzUsIDFdLCBcclxuICAgICAgZGVsYXk6IGlbMF0gKiAwLjEgLy8gU2hvcnRlciBkZWxheXMgZm9yIGJldHRlciBVWFxyXG4gICAgfVxyXG4gIH0pLFxyXG4gIGV4aXQ6IChpOiBudW1iZXJbXSkgPT4gKHtcclxuICAgIHk6IFwiMTAwJVwiLFxyXG4gICAgb3BhY2l0eTogMCxcclxuICAgIHRyYW5zaXRpb246IHsgXHJcbiAgICAgIGR1cmF0aW9uOiAwLjUsIFxyXG4gICAgICBlYXNlOiBbMC42NSwgMCwgMC4zNSwgMV0sIFxyXG4gICAgICBkZWxheTogaVsxXSAqIDAuMDUgLy8gU2hvcnRlciBkZWxheXMgZm9yIGJldHRlciBVWFxyXG4gICAgfVxyXG4gIH0pXHJcbn07XHJcblxyXG4vLyBCbHVyIGFuaW1hdGlvbiAodXNlIHNwYXJpbmdseSBhcyBpdCBjYW4gYmUgcGVyZm9ybWFuY2UtaGVhdnkpXHJcbmV4cG9ydCBjb25zdCBibHVyOiBWYXJpYW50cyA9IHtcclxuICBpbml0aWFsOiB7XHJcbiAgICBmaWx0ZXI6IFwiYmx1cigwcHgpXCIsXHJcbiAgICBvcGFjaXR5OiAxXHJcbiAgfSxcclxuICBvcGVuOiB7XHJcbiAgICBmaWx0ZXI6IFwiYmx1cig0cHgpXCIsXHJcbiAgICBvcGFjaXR5OiAwLjYsXHJcbiAgICB0cmFuc2l0aW9uOiBmYXN0VHJhbnNpdGlvblxyXG4gIH0sXHJcbiAgY2xvc2VkOiB7XHJcbiAgICBmaWx0ZXI6IFwiYmx1cigwcHgpXCIsXHJcbiAgICBvcGFjaXR5OiAxLFxyXG4gICAgdHJhbnNpdGlvbjogZmFzdFRyYW5zaXRpb25cclxuICB9XHJcbn07XHJcblxyXG4vLyBOZXcgYW5pbWF0aW9ucyBmb3IgZW5oYW5jZWQgVUlcclxuXHJcbi8vIEZhZGUgdXAgYW5pbWF0aW9uIGZvciBjb250ZW50IGJsb2Nrc1xyXG5leHBvcnQgY29uc3QgZmFkZVVwOiBWYXJpYW50cyA9IHtcclxuICBoaWRkZW46IHsgb3BhY2l0eTogMCwgeTogMjAgfSxcclxuICB2aXNpYmxlOiB7IFxyXG4gICAgb3BhY2l0eTogMSwgXHJcbiAgICB5OiAwLFxyXG4gICAgdHJhbnNpdGlvbjogeyAuLi5mYXN0VHJhbnNpdGlvbiB9XHJcbiAgfVxyXG59O1xyXG5cclxuLy8gU2NhbGUgYW5pbWF0aW9uIGZvciBidXR0b25zIGFuZCBpbnRlcmFjdGl2ZSBlbGVtZW50c1xyXG5leHBvcnQgY29uc3Qgc2NhbGVVcDogVmFyaWFudHMgPSB7XHJcbiAgaW5pdGlhbDogeyBzY2FsZTogMC45NSwgb3BhY2l0eTogMCB9LFxyXG4gIGFuaW1hdGU6IHsgXHJcbiAgICBzY2FsZTogMSwgXHJcbiAgICBvcGFjaXR5OiAxLFxyXG4gICAgdHJhbnNpdGlvbjogeyBcclxuICAgICAgdHlwZTogXCJzcHJpbmdcIiwgXHJcbiAgICAgIHN0aWZmbmVzczogNDAwLCBcclxuICAgICAgZGFtcGluZzogMTcgXHJcbiAgICB9XHJcbiAgfSxcclxuICB0YXA6IHsgc2NhbGU6IDAuOTcgfVxyXG59O1xyXG5cclxuLy8gU2xpZGUgaW4gZnJvbSBzaWRlIGFuaW1hdGlvblxyXG5leHBvcnQgY29uc3Qgc2xpZGVJbiA9IChkaXJlY3Rpb246IFwibGVmdFwiIHwgXCJyaWdodFwiID0gXCJsZWZ0XCIpOiBWYXJpYW50cyA9PiAoe1xyXG4gIGhpZGRlbjogeyBcclxuICAgIHg6IGRpcmVjdGlvbiA9PT0gXCJsZWZ0XCIgPyAtNjAgOiA2MCwgXHJcbiAgICBvcGFjaXR5OiAwIFxyXG4gIH0sXHJcbiAgdmlzaWJsZTogeyBcclxuICAgIHg6IDAsIFxyXG4gICAgb3BhY2l0eTogMSxcclxuICAgIHRyYW5zaXRpb246IHNtb290aFRyYW5zaXRpb25cclxuICB9XHJcbn0pOyJdLCJuYW1lcyI6WyJmYXN0VHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiZWFzZSIsInNtb290aFRyYW5zaXRpb24iLCJzdGFnZ2VyQ29udGFpbmVyIiwiaGlkZGVuIiwib3BhY2l0eSIsInZpc2libGUiLCJ0cmFuc2l0aW9uIiwic3RhZ2dlckNoaWxkcmVuIiwiZGVsYXlDaGlsZHJlbiIsImluaXRpYWwiLCJvcGVuIiwiY2xvc2VkIiwiYmFja2dyb3VuZCIsImhlaWdodCIsIndpbGxDaGFuZ2UiLCJlbnRlciIsImV4aXQiLCJ0cmFuc2xhdGUiLCJ5IiwiaSIsImRlbGF5IiwiYmx1ciIsImZpbHRlciIsImZhZGVVcCIsInNjYWxlVXAiLCJzY2FsZSIsImFuaW1hdGUiLCJ0eXBlIiwic3RpZmZuZXNzIiwiZGFtcGluZyIsInRhcCIsInNsaWRlSW4iLCJkaXJlY3Rpb24iLCJ4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/anim.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/index.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/header/index.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./style.module.css */ \"(ssr)/./src/components/layout/header/style.module.css\");\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_style_module_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _anim__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./anim */ \"(ssr)/./src/components/layout/header/anim.ts\");\n/* harmony import */ var _nav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./nav */ \"(ssr)/./src/components/layout/header/nav/index.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Header() {\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleRouteChange = ()=>{\n            setIsActive(false);\n        };\n        // Close menu when route changes\n        handleRouteChange();\n    }, [\n        pathname\n    ]);\n    // Add scroll detection\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const isScrolled = window.scrollY > 20;\n            if (isScrolled !== scrolled) {\n                setScrolled(isScrolled);\n            }\n        };\n        // Add event listener\n        window.addEventListener(\"scroll\", handleScroll, {\n            passive: true\n        });\n        // Call once to set initial state\n        handleScroll();\n        // Clean up\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, [\n        scrolled\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        className: `${(_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().header)} ${scrolled ? (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().headerScrolled) : \"\"}`,\n        initial: {\n            y: -100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        transition: {\n            duration: 0.5,\n            ease: \"easeOut\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().bar),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/\",\n                        className: \"ml-2 -my-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Football Club\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 400,\n                                    damping: 10\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    src: \"https://placehold.co/200x200/0284c7/FFFFFF/png?text=FC\",\n                                    alt: \"Football Club Logo\",\n                                    height: 75,\n                                    width: 75,\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        onClick: ()=>{\n                            setIsActive(!isActive);\n                        },\n                        className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().el),\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${(_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().burger)} ${isActive ? (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().burgerActive) : \"\"}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().label),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                        variants: _anim__WEBPACK_IMPORTED_MODULE_5__.opacity,\n                                        animate: !isActive ? \"open\" : \"closed\",\n                                        children: \"Menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                        variants: _anim__WEBPACK_IMPORTED_MODULE_5__.opacity,\n                                        animate: isActive ? \"open\" : \"closed\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                variants: _anim__WEBPACK_IMPORTED_MODULE_5__.background,\n                initial: \"initial\",\n                animate: isActive ? \"open\" : \"closed\",\n                className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().background)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: scrolled ? \"56px\" : \"60px\",\n                    left: 0,\n                    width: \"100%\",\n                    zIndex: 45,\n                    transition: \"top 0.3s ease\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nav__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 51\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\index.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/body/index.tsx":
/*!*********************************************************!*\
  !*** ./src/components/layout/header/nav/body/index.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Body)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./style.module.css */ \"(ssr)/./src/components/layout/header/nav/body/style.module.css\");\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_style_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _anim__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../anim */ \"(ssr)/./src/components/layout/header/anim.ts\");\n\n\n\n\n\nfunction Body({ links, selectedLink, setSelectedLink }) {\n    const getChars = (word)=>{\n        let chars = [];\n        word.split(\"\").forEach((char, i)=>{\n            chars.push(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                custom: [\n                    i * 0.015,\n                    (word.length - i) * 0.008\n                ],\n                variants: _anim__WEBPACK_IMPORTED_MODULE_3__.translate,\n                initial: \"initial\",\n                animate: \"enter\",\n                exit: \"exit\",\n                children: char === \" \" ? \"\\xa0\" : char\n            }, char + i, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\body\\\\index.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this));\n        });\n        return chars;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: (_style_module_css__WEBPACK_IMPORTED_MODULE_2___default().body),\n        variants: _anim__WEBPACK_IMPORTED_MODULE_3__.staggerContainer,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: links.map((link, index)=>{\n            const { title, href } = link;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                variants: {\n                    hidden: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    visible: {\n                        opacity: 1,\n                        y: 0,\n                        transition: {\n                            duration: 0.5,\n                            ease: [\n                                0.25,\n                                0.1,\n                                0.25,\n                                1.0\n                            ],\n                            delay: index * 0.05\n                        }\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: href,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                        onMouseOver: ()=>{\n                            setSelectedLink({\n                                isActive: true,\n                                index\n                            });\n                        },\n                        onMouseLeave: ()=>{\n                            setSelectedLink({\n                                isActive: false,\n                                index\n                            });\n                        },\n                        variants: _anim__WEBPACK_IMPORTED_MODULE_3__.blur,\n                        animate: selectedLink.isActive && selectedLink.index !== index ? \"open\" : \"closed\",\n                        whileHover: {\n                            color: \"#38bdf8\",\n                            transition: {\n                                duration: 0.2\n                            }\n                        },\n                        children: getChars(title)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\body\\\\index.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\body\\\\index.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 13\n                }, this)\n            }, `l_${index}`, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\body\\\\index.tsx\",\n                lineNumber: 53,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\body\\\\index.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/body/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/footer/index.tsx":
/*!***********************************************************!*\
  !*** ./src/components/layout/header/nav/footer/index.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./style.module.css */ \"(ssr)/./src/components/layout/header/nav/footer/style.module.css\");\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_style_module_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n// Animation variants\nconst translate = {\n    initial: {\n        opacity: 0,\n        y: 20\n    },\n    enter: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.5\n        }\n    },\n    exit: {\n        opacity: 0,\n        y: -20,\n        transition: {\n            duration: 0.3\n        }\n    },\n    hover: {\n        scale: 1.05,\n        color: \"#fff\",\n        transition: {\n            duration: 0.2\n        }\n    }\n};\n// Footer links array\nconst footerLinks = [\n    {\n        title: \"Privacy Policy\",\n        href: \"/privacy-policy\"\n    },\n    {\n        title: \"Terms & Conditions\",\n        href: \"/terms-conditions\"\n    },\n    {\n        title: \"Join the Club\",\n        href: \"/contact#join\"\n    }\n];\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().footer),\n        children: footerLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerList),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: link.href,\n                        passHref: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                            custom: [\n                                0.3,\n                                0\n                            ],\n                            variants: translate,\n                            initial: \"initial\",\n                            animate: \"enter\",\n                            exit: \"exit\",\n                            whileHover: \"hover\",\n                            className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerLink),\n                            children: link.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\footer\\\\index.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\footer\\\\index.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\footer\\\\index.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, `footer-link-${index}`, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\footer\\\\index.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\footer\\\\index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/footer/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/image/index.tsx":
/*!**********************************************************!*\
  !*** ./src/components/layout/header/nav/image/index.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./style.module.css */ \"(ssr)/./src/components/layout/header/nav/image/style.module.css\");\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_style_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _anim__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../anim */ \"(ssr)/./src/components/layout/header/anim.ts\");\n\n\n\n\n\n\nfunction ImageComponent({ src, selectedLink }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n        variants: _anim__WEBPACK_IMPORTED_MODULE_4__.opacity,\n        initial: \"initial\",\n        animate: selectedLink.isActive ? \"open\" : \"closed\",\n        className: (_style_module_css__WEBPACK_IMPORTED_MODULE_3___default().imageContainer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: `https://placehold.co/800x600/0284c7/FFFFFF/png?text=${src.replace(\".jpg\", \"\")}`,\n            fill: true,\n            alt: \"Navigation image\",\n            className: \"object-cover rounded-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\image\\\\index.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\image\\\\index.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/image/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/index.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/header/nav/index.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Nav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./style.module.css */ \"(ssr)/./src/components/layout/header/nav/style.module.css\");\n/* harmony import */ var _style_module_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_style_module_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _anim__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../anim */ \"(ssr)/./src/components/layout/header/anim.ts\");\n/* harmony import */ var _body__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./body */ \"(ssr)/./src/components/layout/header/nav/body/index.tsx\");\n/* harmony import */ var _footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./footer */ \"(ssr)/./src/components/layout/header/nav/footer/index.tsx\");\n/* harmony import */ var _image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./image */ \"(ssr)/./src/components/layout/header/nav/image/index.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst links = [\n    {\n        title: \"Home\",\n        href: \"/\",\n        src: \"soccer-field.jpg\"\n    },\n    {\n        title: \"Team\",\n        href: \"/team\",\n        src: \"team.jpg\"\n    },\n    {\n        title: \"Matches\",\n        href: \"/matches\",\n        src: \"match.jpg\"\n    },\n    {\n        title: \"News\",\n        href: \"/news\",\n        src: \"news.jpg\"\n    },\n    {\n        title: \"Gallery\",\n        href: \"/gallery\",\n        src: \"gallery.jpg\"\n    },\n    {\n        title: \"About\",\n        href: \"/about\",\n        src: \"about.jpg\"\n    },\n    {\n        title: \"Contact\",\n        href: \"/contact\",\n        src: \"contact.jpg\"\n    }\n];\nfunction Nav() {\n    const [selectedLink, setSelectedLink] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isActive: false,\n        index: 0\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        variants: _anim__WEBPACK_IMPORTED_MODULE_3__.height,\n        initial: \"initial\",\n        animate: \"enter\",\n        exit: \"exit\",\n        className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().nav),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().wrapper),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_style_module_css__WEBPACK_IMPORTED_MODULE_1___default().container),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_body__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            links: links,\n                            selectedLink: selectedLink,\n                            setSelectedLink: setSelectedLink\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\index.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\index.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\index.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_image__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    src: links[selectedLink.index].src,\n                    selectedLink: selectedLink\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\index.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\index.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\layout\\\\header\\\\nav\\\\index.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/index.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5233f9d21ca7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vdGJhbGwtY2x1Yi13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9iZmYwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTIzM2Y5ZDIxY2E3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/body/style.module.css":
/*!****************************************************************!*\
  !*** ./src/components/layout/header/nav/body/style.module.css ***!
  \****************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"body\": \"style_body__h7bRU\"\n};\n\nmodule.exports.__checksum = \"123acd953084\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL25hdi9ib2R5L3N0eWxlLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vdGJhbGwtY2x1Yi13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L2hlYWRlci9uYXYvYm9keS9zdHlsZS5tb2R1bGUuY3NzPzgyNzYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiYm9keVwiOiBcInN0eWxlX2JvZHlfX2g3YlJVXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjEyM2FjZDk1MzA4NFwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/body/style.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/footer/style.module.css":
/*!******************************************************************!*\
  !*** ./src/components/layout/header/nav/footer/style.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"footer\": \"style_footer__ccpo7\",\n\t\"footerList\": \"style_footerList__fjqnu\",\n\t\"footerLink\": \"style_footerLink__G076M\"\n};\n\nmodule.exports.__checksum = \"9aa369d8ba19\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL25hdi9mb290ZXIvc3R5bGUubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vdGJhbGwtY2x1Yi13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L2hlYWRlci9uYXYvZm9vdGVyL3N0eWxlLm1vZHVsZS5jc3M/NTFjOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJmb290ZXJcIjogXCJzdHlsZV9mb290ZXJfX2NjcG83XCIsXG5cdFwiZm9vdGVyTGlzdFwiOiBcInN0eWxlX2Zvb3Rlckxpc3RfX2ZqcW51XCIsXG5cdFwiZm9vdGVyTGlua1wiOiBcInN0eWxlX2Zvb3RlckxpbmtfX0cwNzZNXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjlhYTM2OWQ4YmExOVwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/footer/style.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/image/style.module.css":
/*!*****************************************************************!*\
  !*** ./src/components/layout/header/nav/image/style.module.css ***!
  \*****************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"imageContainer\": \"style_imageContainer__jqqLl\"\n};\n\nmodule.exports.__checksum = \"b9867ef54ff2\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL25hdi9pbWFnZS9zdHlsZS5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb3RiYWxsLWNsdWItd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9oZWFkZXIvbmF2L2ltYWdlL3N0eWxlLm1vZHVsZS5jc3M/ZDM2YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJpbWFnZUNvbnRhaW5lclwiOiBcInN0eWxlX2ltYWdlQ29udGFpbmVyX19qcXFMbFwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJiOTg2N2VmNTRmZjJcIlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/image/style.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/nav/style.module.css":
/*!***********************************************************!*\
  !*** ./src/components/layout/header/nav/style.module.css ***!
  \***********************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"nav\": \"style_nav__BRI0u\",\n\t\"wrapper\": \"style_wrapper__7fhmG\",\n\t\"container\": \"style_container__nbuSy\"\n};\n\nmodule.exports.__checksum = \"f4b134b23cbd\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL25hdi9zdHlsZS5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb290YmFsbC1jbHViLXdlYnNpdGUvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL25hdi9zdHlsZS5tb2R1bGUuY3NzPzAyMzEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwibmF2XCI6IFwic3R5bGVfbmF2X19CUkkwdVwiLFxuXHRcIndyYXBwZXJcIjogXCJzdHlsZV93cmFwcGVyX183ZmhtR1wiLFxuXHRcImNvbnRhaW5lclwiOiBcInN0eWxlX2NvbnRhaW5lcl9fbmJ1U3lcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZjRiMTM0YjIzY2JkXCJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/nav/style.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header/style.module.css":
/*!*******************************************************!*\
  !*** ./src/components/layout/header/style.module.css ***!
  \*******************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"style_header__xBWZm\",\n\t\"headerScrolled\": \"style_headerScrolled__7QTu3\",\n\t\"bar\": \"style_bar__KBD__\",\n\t\"el\": \"style_el__vVXr1\",\n\t\"label\": \"style_label__0bqbQ\",\n\t\"burger\": \"style_burger__VadDC\",\n\t\"burgerActive\": \"style_burgerActive__DMQbO\",\n\t\"background\": \"style_background__4y8xu\"\n};\n\nmodule.exports.__checksum = \"e8cf4d4c29c0\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL3N0eWxlLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb290YmFsbC1jbHViLXdlYnNpdGUvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyL3N0eWxlLm1vZHVsZS5jc3M/ZjI4NSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJoZWFkZXJcIjogXCJzdHlsZV9oZWFkZXJfX3hCV1ptXCIsXG5cdFwiaGVhZGVyU2Nyb2xsZWRcIjogXCJzdHlsZV9oZWFkZXJTY3JvbGxlZF9fN1FUdTNcIixcblx0XCJiYXJcIjogXCJzdHlsZV9iYXJfX0tCRF9fXCIsXG5cdFwiZWxcIjogXCJzdHlsZV9lbF9fdlZYcjFcIixcblx0XCJsYWJlbFwiOiBcInN0eWxlX2xhYmVsX18wYnFiUVwiLFxuXHRcImJ1cmdlclwiOiBcInN0eWxlX2J1cmdlcl9fVmFkRENcIixcblx0XCJidXJnZXJBY3RpdmVcIjogXCJzdHlsZV9idXJnZXJBY3RpdmVfX0RNUWJPXCIsXG5cdFwiYmFja2dyb3VuZFwiOiBcInN0eWxlX2JhY2tncm91bmRfXzR5OHh1XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImU4Y2Y0ZDRjMjljMFwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header/style.module.css\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\app\error.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\",\"preload\":true}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_preload_true_variableName_montserrat___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-montserrat\",\"preload\":true}],\"variableName\":\"montserrat\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Montserrat\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-montserrat\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"montserrat\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_preload_true_variableName_montserrat___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_preload_true_variableName_montserrat___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Football Club | Official Website\",\n    description: \"The official website of our football club featuring team information, match schedules, news, and more.\",\n    viewport: \"width=device-width, initial-scale=1, maximum-scale=5\",\n    themeColor: \"#0284c7\",\n    // Add additional metadata for better SEO and social sharing\n    openGraph: {\n        type: \"website\",\n        title: \"Football Club | Official Website\",\n        description: \"The official website of our football club featuring team information, match schedules, news, and more.\",\n        images: [\n            {\n                url: \"https://placehold.co/1200x630/0284c7/FFFFFF/png?text=Football+Club\",\n                width: 1200,\n                height: 630,\n                alt: \"Football Club\"\n            }\n        ]\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_preload_true_variableName_montserrat___WEBPACK_IMPORTED_MODULE_6___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://placehold.co\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://placehold.co\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen flex flex-col bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-grow\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        id: \"performance-monitor\",\n                        strategy: \"afterInteractive\",\n                        children: `\r\n            // Simple performance monitoring\r\n            window.addEventListener('load', () => {\r\n              // Report performance metrics\r\n              if (performance && 'getEntriesByType' in performance) {\r\n                setTimeout(() => {\r\n                  const navEntry = performance.getEntriesByType('navigation')[0];\r\n                  const paintEntries = performance.getEntriesByType('paint');\r\n                  \r\n                  if (navEntry) {\r\n                    console.log('Page load time:', Math.round(navEntry.duration), 'ms');\r\n                  }\r\n                  \r\n                  if (paintEntries.length) {\r\n                    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');\r\n                    if (fcp) {\r\n                      console.log('First Contentful Paint:', Math.round(fcp.startTime), 'ms');\r\n                    }\r\n                  }\r\n                  \r\n                  // Clear performance buffer to save memory\r\n                  if ('clearResourceTimings' in performance) {\r\n                    performance.clearResourceTimings();\r\n                  }\r\n                }, 0);\r\n              }\r\n            });\r\n            \r\n            // Detect slow connections and reduce animations\r\n            if (navigator.connection && \r\n                (navigator.connection.effectiveType === 'slow-2g' || \r\n                 navigator.connection.effectiveType === '2g')) {\r\n              document.documentElement.classList.add('reduce-motion');\r\n            }\r\n            \r\n            // Detect reduced motion preference\r\n            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {\r\n              document.documentElement.classList.add('reduce-motion');\r\n            }\r\n          `\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\app\loading.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/mwenye-kiti/layout.tsx":
/*!****************************************!*\
  !*** ./src/app/mwenye-kiti/layout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\app\mwenye-kiti\layout.tsx#default`));

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\app\mwenye-kiti\layout.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./src/app/mwenye-kiti/login/page.tsx":
/*!********************************************!*\
  !*** ./src/app/mwenye-kiti/login/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\app\mwenye-kiti\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\app\not-found.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\components\layout\Footer.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\projects\ofc\client\src\components\layout\Header.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmwenye-kiti%2Flogin%2Fpage&page=%2Fmwenye-kiti%2Flogin%2Fpage&appPaths=%2Fmwenye-kiti%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fmwenye-kiti%2Flogin%2Fpage.tsx&appDir=C%3A%5Cprojects%5Cofc%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cprojects%5Cofc%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
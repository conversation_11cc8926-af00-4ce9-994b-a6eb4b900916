"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UpcomingMatches; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/animations */ \"(app-pages-browser)/./src/lib/animations.ts\");\n/* harmony import */ var _lib_hooks__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/hooks */ \"(app-pages-browser)/./src/lib/hooks.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction UpcomingMatches() {\n    var _matches_filter;\n    _s();\n    const [reducedMotion, setReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: matches, loading, error } = (0,_lib_hooks__WEBPACK_IMPORTED_MODULE_5__.useMatches)();\n    // Filter for upcoming matches and limit to 3\n    const upcomingMatches = (matches === null || matches === void 0 ? void 0 : (_matches_filter = matches.filter((match)=>match.match_status === \"scheduled\" || match.result === \"upcoming\")) === null || _matches_filter === void 0 ? void 0 : _matches_filter.slice(0, 3)) || [];\n    // Check for reduced motion preference\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setReducedMotion(window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches || document.documentElement.classList.contains(\"reduce-motion\"));\n    }, []);\n    // Animation variants with conditional reduced motion\n    const getAnimationProps = (animation)=>{\n        if (reducedMotion) {\n            return {\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.5\n                }\n            };\n        }\n        return animation;\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"Loading upcoming matches...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: [\n                            \"Error loading matches: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    // No matches state\n    if (upcomingMatches.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: \"Upcoming Matches\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No upcoming matches scheduled at the moment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section bg-gray-50 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        className: \"absolute -top-20 right-[10%] w-40 h-40 rounded-full bg-primary-100 opacity-30\",\n                        animate: reducedMotion ? {} : {\n                            y: [\n                                0,\n                                -30,\n                                0\n                            ],\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ],\n                            opacity: [\n                                0.3,\n                                0.5,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            repeat: Infinity,\n                            duration: 10,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        className: \"absolute bottom-20 left-[5%] w-64 h-64 rounded-full bg-secondary-100 opacity-20\",\n                        animate: reducedMotion ? {} : {\n                            y: [\n                                0,\n                                40,\n                                0\n                            ],\n                            scale: [\n                                1,\n                                1.15,\n                                1\n                            ],\n                            opacity: [\n                                0.2,\n                                0.4,\n                                0.2\n                            ]\n                        },\n                        transition: {\n                            repeat: Infinity,\n                            duration: 15,\n                            ease: \"easeInOut\",\n                            delay: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        ...getAnimationProps({\n                            initial: \"hidden\",\n                            whileInView: \"visible\",\n                            viewport: {\n                                once: true,\n                                margin: \"-100px\"\n                            }\n                        }),\n                        variants: _lib_animations__WEBPACK_IMPORTED_MODULE_4__.staggerContainer,\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                variants: _lib_animations__WEBPACK_IMPORTED_MODULE_4__.fadeUp,\n                                className: \"mb-2\",\n                                children: \"Upcoming Matches\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: _lib_animations__WEBPACK_IMPORTED_MODULE_4__.scaleUp,\n                                className: \"w-20 h-1 bg-primary-500 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                variants: _lib_animations__WEBPACK_IMPORTED_MODULE_4__.fadeUp,\n                                className: \"text-gray-600 text-lg\",\n                                children: \"Support our team in these upcoming fixtures\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        ...getAnimationProps({\n                            initial: \"hidden\",\n                            whileInView: \"visible\",\n                            viewport: {\n                                once: true,\n                                margin: \"-50px\"\n                            }\n                        }),\n                        variants: _lib_animations__WEBPACK_IMPORTED_MODULE_4__.staggerContainer,\n                        className: \"grid sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: upcomingMatches.map((match, index)=>{\n                            // Determine if this is a home or away match for \"Our FC\"\n                            const isHomeMatch = match.location.toLowerCase().includes(\"home\") || match.location.toLowerCase().includes(\"stadium\");\n                            const ourTeamName = \"Our FC\";\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: {\n                                    hidden: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    visible: {\n                                        opacity: 1,\n                                        y: 0,\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: index * 0.1\n                                        }\n                                    }\n                                },\n                                whileHover: reducedMotion ? {} : {\n                                    y: -10,\n                                    boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\"\n                                },\n                                transition: {\n                                    type: \"spring\",\n                                    stiffness: 300,\n                                    damping: 15\n                                },\n                                className: \"card hover:shadow-lg transition-all duration-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-1 bg-green-600 text-white text-center text-sm font-medium\",\n                                        children: match.competition || \"Match\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                className: \"relative h-14 sm:h-16 w-14 sm:w-16 mx-auto mb-2\",\n                                                                whileHover: reducedMotion ? {} : {\n                                                                    scale: 1.1\n                                                                },\n                                                                transition: {\n                                                                    type: \"spring\",\n                                                                    stiffness: 300,\n                                                                    damping: 10\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getImageUrl)(match.image_url) || \"https://placehold.co/200x200/0284c7/FFFFFF/png?text=FC\",\n                                                                    alt: isHomeMatch ? ourTeamName : match.opponent,\n                                                                    fill: true,\n                                                                    sizes: \"64px\",\n                                                                    className: \"object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-sm sm:text-base\",\n                                                                children: isHomeMatch ? ourTeamName : match.opponent\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                        className: \"text-center px-2 sm:px-4\",\n                                                        animate: reducedMotion ? {} : {\n                                                            scale: [\n                                                                1,\n                                                                1.1,\n                                                                1\n                                                            ],\n                                                            color: [\n                                                                \"#1e293b\",\n                                                                \"#0284c7\",\n                                                                \"#1e293b\"\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            repeat: Infinity,\n                                                            duration: 3,\n                                                            ease: \"easeInOut\",\n                                                            delay: index * 0.5\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xl sm:text-2xl font-bold text-gray-800\",\n                                                            children: \"VS\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                className: \"relative h-14 sm:h-16 w-14 sm:w-16 mx-auto mb-2\",\n                                                                whileHover: reducedMotion ? {} : {\n                                                                    scale: 1.1\n                                                                },\n                                                                transition: {\n                                                                    type: \"spring\",\n                                                                    stiffness: 300,\n                                                                    damping: 10\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: \"https://placehold.co/200x200/6d28d9/FFFFFF/png?text=\" + match.opponent.substring(0, 2),\n                                                                    alt: isHomeMatch ? match.opponent : ourTeamName,\n                                                                    fill: true,\n                                                                    sizes: \"64px\",\n                                                                    className: \"object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-sm sm:text-base\",\n                                                                children: isHomeMatch ? match.opponent : ourTeamName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row sm:justify-between text-sm text-gray-600 mb-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-4 w-4 mr-1 text-green-500\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(match.match_date)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-4 w-4 mr-1 text-green-500\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                            lineNumber: 252,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatTime)(match.match_time)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 mr-1 text-green-500\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            match.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 sm:px-6 pb-4 sm:pb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            whileHover: reducedMotion ? {} : {\n                                                scale: 1.03\n                                            },\n                                            whileTap: reducedMotion ? {} : {\n                                                scale: 0.98\n                                            },\n                                            transition: {\n                                                type: \"spring\",\n                                                stiffness: 400,\n                                                damping: 17\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/matches/\".concat(match.id),\n                                                className: \"btn-outline w-full block text-center text-sm sm:text-base\",\n                                                children: \"Match Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, match.id, true, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        className: \"text-center mt-8 sm:mt-10\",\n                        initial: reducedMotion ? {} : {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: reducedMotion ? {} : {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            whileHover: reducedMotion ? {} : {\n                                scale: 1.05\n                            },\n                            whileTap: reducedMotion ? {} : {\n                                scale: 0.98\n                            },\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 400,\n                                damping: 17\n                            },\n                            className: \"inline-block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/matches\",\n                                className: \"btn-primary\",\n                                children: \"View All Matches\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\UpcomingMatches.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(UpcomingMatches, \"JT07sPDIGzbAwQDtaZDDmDFtUio=\", false, function() {\n    return [\n        _lib_hooks__WEBPACK_IMPORTED_MODULE_5__.useMatches\n    ];\n});\n_c = UpcomingMatches;\nvar _c;\n$RefreshReg$(_c, \"UpcomingMatches\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});
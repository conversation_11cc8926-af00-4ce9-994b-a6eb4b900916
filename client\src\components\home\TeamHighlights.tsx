'use client'

import Link from 'next/link'
import Image from 'next/image'
import { usePlayers } from '@/lib/hooks'
import { getImageUrl } from '@/lib/utils'
export default function TeamHighlights() {
  const { data: players, loading, error } = usePlayers()

  // Get first 4 players for highlights
  const featuredPlayers = players?.slice(0, 4) || []

  // Loading state
  if (loading) {
    return (
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading team highlights...</p>
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center">
            <p className="text-red-600">Error loading players: {error}</p>
          </div>
        </div>
      </section>
    )
  }

  // No players state
  if (featuredPlayers.length === 0) {
    return (
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Team Highlights</h2>
            <p className="text-gray-600">No players available at the moment.</p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="section bg-gray-50">
      <div className="container-custom">
        <div className="text-center mb-12">
          <h2 className="mb-2">Team Highlights</h2>
          <p className="text-gray-600 text-lg">Meet some of our star players</p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {featuredPlayers.map((player) => (
            <div key={player.id} className="card hover:shadow-lg transition-shadow overflow-hidden group">
              <div className="relative h-80 overflow-hidden">
                <Image
                  src={getImageUrl(player.image_url) || 'https://placehold.co/400x600/0284c7/FFFFFF/png?text=Player'}
                  alt={player.name}
                  fill
                  className="object-cover object-top"
                />
                {player.jersey_number && (
                  <div className="absolute top-0 left-0 bg-green-600 text-white text-2xl font-bold w-12 h-12 flex items-center justify-center">
                    {player.jersey_number}
                  </div>
                )}
              </div>
              <div className="p-6">
                <h3 className="font-bold text-xl mb-1">{player.name}</h3>
                <p className="text-green-600 font-medium mb-4">{player.position}</p>

                <div className="grid grid-cols-2 gap-2 mb-4">
                  {player.nationality && (
                    <div className="text-center">
                      <div className="text-sm font-bold text-gray-800">{player.nationality}</div>
                      <div className="text-xs text-gray-500">Nationality</div>
                    </div>
                  )}
                  {player.height && (
                    <div className="text-center">
                      <div className="text-sm font-bold text-gray-800">{player.height}cm</div>
                      <div className="text-xs text-gray-500">Height</div>
                    </div>
                  )}
                </div>

                <Link href={`/team/${player.id}`} className="btn-outline w-full block text-center">
                  Player Profile
                </Link>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-8">
          <Link href="/team" className="btn-primary">
            View Full Team
          </Link>
        </div>
      </div>
    </section>
  )
}
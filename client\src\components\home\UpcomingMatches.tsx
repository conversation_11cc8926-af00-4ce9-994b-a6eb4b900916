'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { useEffect, useState } from 'react'
import { staggerContainer, fadeUp, scaleUp } from '@/lib/animations'
import { useMatches } from '@/lib/hooks'
import { formatDate, formatTime, getImageUrl } from '@/lib/utils'
import { Match } from '@/lib/types'

export default function UpcomingMatches() {
  const [reducedMotion, setReducedMotion] = useState(false)
  const { data: matches, loading, error } = useMatches()

  // Filter for upcoming matches and limit to 3
  const upcomingMatches = matches
    ?.filter((match: Match) => match.match_status === 'scheduled' || match.result === 'upcoming')
    ?.slice(0, 3) || []

  // Check for reduced motion preference
  useEffect(() => {
    setReducedMotion(
      window.matchMedia('(prefers-reduced-motion: reduce)').matches ||
      document.documentElement.classList.contains('reduce-motion')
    )
  }, [])

  // Animation variants with conditional reduced motion
  const getAnimationProps = (animation: any) => {
    if (reducedMotion) {
      return {
        initial: { opacity: 0 },
        whileInView: { opacity: 1 },
        transition: { duration: 0.5 }
      }
    }
    return animation
  }

  // Loading state
  if (loading) {
    return (
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading upcoming matches...</p>
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center">
            <p className="text-red-600">Error loading matches: {error}</p>
          </div>
        </div>
      </section>
    )
  }

  // No matches state
  if (upcomingMatches.length === 0) {
    return (
      <section className="section bg-gray-50">
        <div className="container-custom">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Upcoming Matches</h2>
            <p className="text-gray-600">No upcoming matches scheduled at the moment.</p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="section bg-gray-50 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <motion.div 
          className="absolute -top-20 right-[10%] w-40 h-40 rounded-full bg-primary-100 opacity-30"
          animate={reducedMotion ? {} : { 
            y: [0, -30, 0],
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{ 
            repeat: Infinity,
            duration: 10,
            ease: "easeInOut"
          }}
        />
        
        <motion.div 
          className="absolute bottom-20 left-[5%] w-64 h-64 rounded-full bg-secondary-100 opacity-20"
          animate={reducedMotion ? {} : { 
            y: [0, 40, 0],
            scale: [1, 1.15, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{ 
            repeat: Infinity,
            duration: 15,
            ease: "easeInOut",
            delay: 1
          }}
        />
      </div>
      
      <div className="container-custom relative z-10">
        <motion.div
          {...getAnimationProps({
            initial: "hidden",
            whileInView: "visible",
            viewport: { once: true, margin: "-100px" }
          })}
          variants={staggerContainer}
          className="text-center mb-12"
        >
          <motion.h2 
            variants={fadeUp}
            className="mb-2"
          >
            Upcoming Matches
          </motion.h2>
          
          <motion.div 
            variants={scaleUp}
            className="w-20 h-1 bg-primary-500 mx-auto mb-4"
          />
          
          <motion.p 
            variants={fadeUp}
            className="text-gray-600 text-lg"
          >
            Support our team in these upcoming fixtures
          </motion.p>
        </motion.div>
        
        <motion.div
          {...getAnimationProps({
            initial: "hidden",
            whileInView: "visible",
            viewport: { once: true, margin: "-50px" }
          })}
          variants={staggerContainer}
          className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {upcomingMatches.map((match, index) => {
            // Determine if this is a home or away match for "Our FC"
            const isHomeMatch = match.location.toLowerCase().includes('home') ||
                               match.location.toLowerCase().includes('stadium');
            const ourTeamName = 'Our FC';

            return (
              <motion.div
                key={match.id}
                variants={{
                  hidden: { opacity: 0, y: 30 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: {
                      duration: 0.5,
                      delay: index * 0.1
                    }
                  }
                }}
                whileHover={reducedMotion ? {} : {
                  y: -10,
                  boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                }}
                transition={{ type: "spring", stiffness: 300, damping: 15 }}
                className="card hover:shadow-lg transition-all duration-300"
              >
                <div className="p-1 bg-green-600 text-white text-center text-sm font-medium">
                  {match.competition || 'Match'}
                </div>
                <div className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-center flex-1">
                      <motion.div
                        className="relative h-14 sm:h-16 w-14 sm:w-16 mx-auto mb-2"
                        whileHover={reducedMotion ? {} : { scale: 1.1 }}
                        transition={{ type: "spring", stiffness: 300, damping: 10 }}
                      >
                        <Image
                          src={getImageUrl(match.image_url) || 'https://placehold.co/200x200/0284c7/FFFFFF/png?text=FC'}
                          alt={isHomeMatch ? ourTeamName : match.opponent}
                          fill
                          sizes="64px"
                          className="object-contain"
                        />
                      </motion.div>
                      <h4 className="font-semibold text-sm sm:text-base">
                        {isHomeMatch ? ourTeamName : match.opponent}
                      </h4>
                    </div>

                    <motion.div
                      className="text-center px-2 sm:px-4"
                      animate={reducedMotion ? {} : {
                        scale: [1, 1.1, 1],
                        color: ["#1e293b", "#0284c7", "#1e293b"]
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut",
                        delay: index * 0.5
                      }}
                    >
                      <div className="text-xl sm:text-2xl font-bold text-gray-800">VS</div>
                    </motion.div>

                    <div className="text-center flex-1">
                      <motion.div
                        className="relative h-14 sm:h-16 w-14 sm:w-16 mx-auto mb-2"
                        whileHover={reducedMotion ? {} : { scale: 1.1 }}
                        transition={{ type: "spring", stiffness: 300, damping: 10 }}
                      >
                        <Image
                          src={'https://placehold.co/200x200/6d28d9/FFFFFF/png?text=' + match.opponent.substring(0, 2)}
                          alt={isHomeMatch ? match.opponent : ourTeamName}
                          fill
                          sizes="64px"
                          className="object-contain"
                        />
                      </motion.div>
                      <h4 className="font-semibold text-sm sm:text-base">
                        {isHomeMatch ? match.opponent : ourTeamName}
                      </h4>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex flex-col sm:flex-row sm:justify-between text-sm text-gray-600 mb-2 gap-2">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {formatDate(match.match_date)}
                      </div>
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {formatTime(match.match_time)}
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      {match.location}
                    </div>
                  </div>
                </div>
                <div className="px-4 sm:px-6 pb-4 sm:pb-6">
                  <motion.div
                    whileHover={reducedMotion ? {} : { scale: 1.03 }}
                    whileTap={reducedMotion ? {} : { scale: 0.98 }}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                  >
                    <Link href={`/matches/${match.id}`} className="btn-outline w-full block text-center text-sm sm:text-base">
                      Match Details
                    </Link>
                  </motion.div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>
        
        <motion.div 
          className="text-center mt-8 sm:mt-10"
          initial={reducedMotion ? {} : { opacity: 0, y: 20 }}
          whileInView={reducedMotion ? {} : { opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <motion.div
            whileHover={reducedMotion ? {} : { scale: 1.05 }}
            whileTap={reducedMotion ? {} : { scale: 0.98 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
            className="inline-block"
          >
            <Link href="/matches" className="btn-primary">
              View All Matches
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
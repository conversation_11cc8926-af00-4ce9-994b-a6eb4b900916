'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import { useCreateCoach } from '@/lib/hooks'
import { CreateCoachRequest } from '@/lib/types'

export default function NewCoach() {
  const router = useRouter()
  const createCoach = useCreateCoach()
  
  const [formData, setFormData] = useState<CreateCoachRequest>({
    name: '',
    specialty: '',
    nationality: '',
    experience_years: undefined,
    date_joined: '',
    bio: '',
    achievements: '',
    image_url: '',
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const specialties = [
    'Head Coach',
    'Assistant Coach',
    'Goalkeeper Coach',
    'Fitness Coach',
    'Technical Coach',
    'Youth Coach',
    'Tactical Analyst',
    'Set Piece Specialist',
    'Defensive Coach',
    'Attacking Coach'
  ]

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (formData.experience_years && formData.experience_years < 0) {
      newErrors.experience_years = 'Experience years cannot be negative'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const result = await createCoach.mutate(formData)
    if (result) {
      router.push('/mwenye-kiti/coaches')
    }
  }

  const handleInputChange = (field: keyof CreateCoachRequest, value: string | number | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="mb-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Link
            href="/mwenye-kiti/coaches"
            className="inline-flex items-center text-sm text-gray-500 hover:text-green-600 mb-4 transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Coaches
          </Link>
        </motion.div>
        <motion.h1 
          className="text-3xl font-bold bg-gradient-to-r from-green-600 to-red-600 bg-clip-text text-transparent"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Add New Coach
        </motion.h1>
        <motion.p 
          className="mt-1 text-sm text-gray-600"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Add a new coach to your coaching staff.
        </motion.p>
      </div>

      {/* Form */}
      <motion.div 
        className="bg-white shadow-lg rounded-xl border border-gray-100"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <form onSubmit={handleSubmit} className="space-y-6 p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Name */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.name ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="Enter coach name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </motion.div>

            {/* Specialty */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <label htmlFor="specialty" className="block text-sm font-medium text-gray-700">
                Specialty
              </label>
              <select
                id="specialty"
                value={formData.specialty || ''}
                onChange={(e) => handleInputChange('specialty', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              >
                <option value="">Select specialty</option>
                {specialties.map(specialty => (
                  <option key={specialty} value={specialty}>{specialty}</option>
                ))}
              </select>
            </motion.div>

            {/* Nationality */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
            >
              <label htmlFor="nationality" className="block text-sm font-medium text-gray-700">
                Nationality
              </label>
              <input
                type="text"
                id="nationality"
                value={formData.nationality || ''}
                onChange={(e) => handleInputChange('nationality', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="e.g., Kenya"
              />
            </motion.div>

            {/* Experience Years */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.7 }}
            >
              <label htmlFor="experience_years" className="block text-sm font-medium text-gray-700">
                Experience (Years)
              </label>
              <input
                type="number"
                id="experience_years"
                min="0"
                max="50"
                value={formData.experience_years || ''}
                onChange={(e) => handleInputChange('experience_years', e.target.value ? parseInt(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.experience_years ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="e.g., 10"
              />
              {errors.experience_years && <p className="mt-1 text-sm text-red-600">{errors.experience_years}</p>}
            </motion.div>

            {/* Date Joined */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.8 }}
            >
              <label htmlFor="date_joined" className="block text-sm font-medium text-gray-700">
                Date Joined
              </label>
              <input
                type="date"
                id="date_joined"
                value={formData.date_joined || ''}
                onChange={(e) => handleInputChange('date_joined', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              />
            </motion.div>

            {/* Image URL */}
            <motion.div 
              className="sm:col-span-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.9 }}
            >
              <label htmlFor="image_url" className="block text-sm font-medium text-gray-700">
                Profile Image URL
              </label>
              <input
                type="url"
                id="image_url"
                value={formData.image_url || ''}
                onChange={(e) => handleInputChange('image_url', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="https://example.com/coach-image.jpg"
              />
            </motion.div>
          </div>

          {/* Bio */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.0 }}
          >
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
              Biography
            </label>
            <textarea
              id="bio"
              rows={4}
              value={formData.bio || ''}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              placeholder="Tell us about this coach..."
            />
          </motion.div>

          {/* Achievements */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.1 }}
          >
            <label htmlFor="achievements" className="block text-sm font-medium text-gray-700">
              Achievements
            </label>
            <textarea
              id="achievements"
              rows={3}
              value={formData.achievements || ''}
              onChange={(e) => handleInputChange('achievements', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              placeholder="List major achievements and awards..."
            />
          </motion.div>

          {/* Error Message */}
          {createCoach.error && (
            <motion.div 
              className="rounded-lg bg-red-50 border border-red-200 p-4"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              <div className="text-sm text-red-700">
                Error creating coach: {createCoach.error}
              </div>
            </motion.div>
          )}

          {/* Submit Buttons */}
          <motion.div 
            className="flex justify-end space-x-3"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.2 }}
          >
            <Link
              href="/mwenye-kiti/coaches"
              className="bg-white py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
            >
              Cancel
            </Link>
            <motion.button
              type="submit"
              disabled={createCoach.loading}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 transition-all duration-200"
            >
              {createCoach.loading ? 'Adding...' : 'Add Coach'}
            </motion.button>
          </motion.div>
        </form>
      </motion.div>
    </motion.div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { ArrowLeftIcon } from '@heroicons/react/outline'
import { usePlayer, useUpdatePlayer } from '@/lib/hooks'
import { UpdatePlayerRequest } from '@/lib/types'

export default function EditPlayer({ params }: { params: { id: string } }) {
  const router = useRouter()
  const playerId = parseInt(params.id)
  const { data: player, loading: playerLoading, error: playerError } = usePlayer(playerId)
  const updatePlayer = useUpdatePlayer()
  
  const [formData, setFormData] = useState<UpdatePlayerRequest>({
    name: '',
    position: '',
    jersey_number: undefined,
    date_of_birth: '',
    nationality: '',
    height: undefined,
    weight: undefined,
    bio: '',
    image_url: '',
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const positions = [
    'Goalkeeper',
    'Defender',
    'Midfielder',
    'Forward',
    'Winger',
    'Striker',
    'Centre-back',
    'Full-back',
    'Wing-back',
    'Defensive Midfielder',
    'Central Midfielder',
    'Attacking Midfielder',
  ]

  // Populate form when player data loads
  useEffect(() => {
    if (player) {
      setFormData({
        name: player.name,
        position: player.position,
        jersey_number: player.jersey_number,
        date_of_birth: player.date_of_birth || '',
        nationality: player.nationality || '',
        height: player.height,
        weight: player.weight,
        bio: player.bio || '',
        image_url: player.image_url || '',
      })
    }
  }, [player])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.position) {
      newErrors.position = 'Position is required'
    }

    if (formData.jersey_number && (formData.jersey_number < 1 || formData.jersey_number > 99)) {
      newErrors.jersey_number = 'Jersey number must be between 1 and 99'
    }

    if (formData.height && formData.height < 100) {
      newErrors.height = 'Height must be at least 100cm'
    }

    if (formData.weight && formData.weight < 30) {
      newErrors.weight = 'Weight must be at least 30kg'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const result = await updatePlayer.mutate({ id: playerId, data: formData })
    if (result) {
      router.push('/admin/players')
    }
  }

  const handleInputChange = (field: keyof UpdatePlayerRequest, value: string | number | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  if (playerLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (playerError || !player) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Error loading player: {playerError || 'Player not found'}</p>
        <Link
          href="/admin/players"
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
        >
          Back to Players
        </Link>
      </div>
    )
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <Link
          href="/admin/players"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Players
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">Edit Player</h1>
        <p className="mt-1 text-sm text-gray-600">
          Update the information for {player.name}.
        </p>
      </div>

      {/* Form */}
      <div className="bg-white shadow rounded-lg">
        <form onSubmit={handleSubmit} className="space-y-6 p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
                  errors.name ? 'border-red-300' : ''
                }`}
                placeholder="Enter player name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* Position */}
            <div>
              <label htmlFor="position" className="block text-sm font-medium text-gray-700">
                Position *
              </label>
              <select
                id="position"
                value={formData.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
                  errors.position ? 'border-red-300' : ''
                }`}
              >
                <option value="">Select position</option>
                {positions.map(position => (
                  <option key={position} value={position}>{position}</option>
                ))}
              </select>
              {errors.position && <p className="mt-1 text-sm text-red-600">{errors.position}</p>}
            </div>

            {/* Jersey Number */}
            <div>
              <label htmlFor="jersey_number" className="block text-sm font-medium text-gray-700">
                Jersey Number
              </label>
              <input
                type="number"
                id="jersey_number"
                min="1"
                max="99"
                value={formData.jersey_number || ''}
                onChange={(e) => handleInputChange('jersey_number', e.target.value ? parseInt(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
                  errors.jersey_number ? 'border-red-300' : ''
                }`}
                placeholder="e.g., 10"
              />
              {errors.jersey_number && <p className="mt-1 text-sm text-red-600">{errors.jersey_number}</p>}
            </div>

            {/* Date of Birth */}
            <div>
              <label htmlFor="date_of_birth" className="block text-sm font-medium text-gray-700">
                Date of Birth
              </label>
              <input
                type="date"
                id="date_of_birth"
                value={formData.date_of_birth}
                onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Nationality */}
            <div>
              <label htmlFor="nationality" className="block text-sm font-medium text-gray-700">
                Nationality
              </label>
              <input
                type="text"
                id="nationality"
                value={formData.nationality || ''}
                onChange={(e) => handleInputChange('nationality', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="e.g., England"
              />
            </div>

            {/* Height */}
            <div>
              <label htmlFor="height" className="block text-sm font-medium text-gray-700">
                Height (cm)
              </label>
              <input
                type="number"
                id="height"
                min="100"
                max="250"
                value={formData.height || ''}
                onChange={(e) => handleInputChange('height', e.target.value ? parseFloat(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
                  errors.height ? 'border-red-300' : ''
                }`}
                placeholder="e.g., 180"
              />
              {errors.height && <p className="mt-1 text-sm text-red-600">{errors.height}</p>}
            </div>

            {/* Weight */}
            <div>
              <label htmlFor="weight" className="block text-sm font-medium text-gray-700">
                Weight (kg)
              </label>
              <input
                type="number"
                id="weight"
                min="30"
                max="150"
                value={formData.weight || ''}
                onChange={(e) => handleInputChange('weight', e.target.value ? parseFloat(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
                  errors.weight ? 'border-red-300' : ''
                }`}
                placeholder="e.g., 75"
              />
              {errors.weight && <p className="mt-1 text-sm text-red-600">{errors.weight}</p>}
            </div>

            {/* Image URL */}
            <div className="sm:col-span-2">
              <label htmlFor="image_url" className="block text-sm font-medium text-gray-700">
                Image URL
              </label>
              <input
                type="url"
                id="image_url"
                value={formData.image_url || ''}
                onChange={(e) => handleInputChange('image_url', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                placeholder="https://example.com/player-image.jpg"
              />
            </div>
          </div>

          {/* Bio */}
          <div>
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
              Biography
            </label>
            <textarea
              id="bio"
              rows={4}
              value={formData.bio || ''}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              placeholder="Tell us about this player..."
            />
          </div>

          {/* Error Message */}
          {updatePlayer.error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">
                Error updating player: {updatePlayer.error}
              </div>
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3">
            <Link
              href="/admin/players"
              className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={updatePlayer.loading}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {updatePlayer.loading ? 'Updating...' : 'Update Player'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

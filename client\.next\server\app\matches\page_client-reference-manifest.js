globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/matches/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(ssr)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/loading.tsx":{"*":{"id":"(ssr)/./src/app/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/matches/page.tsx":{"*":{"id":"(ssr)/./src/app/matches/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/gallery/page.tsx":{"*":{"id":"(ssr)/./src/app/gallery/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/mwenye-kiti/login/page.tsx":{"*":{"id":"(ssr)/./src/app/mwenye-kiti/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/mwenye-kiti/layout.tsx":{"*":{"id":"(ssr)/./src/app/mwenye-kiti/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/mwenye-kiti/page.tsx":{"*":{"id":"(ssr)/./src/app/mwenye-kiti/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\projects\\ofc\\client\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\",\"preload\":true}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\",\"preload\":true}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-montserrat\",\"preload\":true}],\"variableName\":\"montserrat\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-montserrat\",\"preload\":true}],\"variableName\":\"montserrat\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\projects\\ofc\\client\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\projects\\ofc\\client\\src\\components\\layout\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/layout/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\projects\\ofc\\client\\src\\components\\layout\\Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\projects\\ofc\\client\\src\\app\\error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\projects\\ofc\\client\\src\\app\\loading.tsx":{"id":"(app-pages-browser)/./src/app/loading.tsx","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"C:\\projects\\ofc\\client\\src\\app\\not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false},"C:\\projects\\ofc\\client\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false},"C:\\projects\\ofc\\client\\src\\app\\matches\\page.tsx":{"id":"(app-pages-browser)/./src/app/matches/page.tsx","name":"*","chunks":["app/matches/page","static/chunks/app/matches/page.js"],"async":false},"C:\\projects\\ofc\\client\\src\\app\\gallery\\page.tsx":{"id":"(app-pages-browser)/./src/app/gallery/page.tsx","name":"*","chunks":[],"async":false},"C:\\projects\\ofc\\client\\src\\app\\mwenye-kiti\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/mwenye-kiti/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\projects\\ofc\\client\\src\\app\\mwenye-kiti\\layout.tsx":{"id":"(app-pages-browser)/./src/app/mwenye-kiti/layout.tsx","name":"*","chunks":[],"async":false},"C:\\projects\\ofc\\client\\src\\app\\mwenye-kiti\\page.tsx":{"id":"(app-pages-browser)/./src/app/mwenye-kiti/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\projects\\ofc\\client\\src\\":[],"C:\\projects\\ofc\\client\\src\\app\\page":[],"C:\\projects\\ofc\\client\\src\\app\\layout":["static/css/app/layout.css"],"C:\\projects\\ofc\\client\\src\\app\\error":[],"C:\\projects\\ofc\\client\\src\\app\\loading":[],"C:\\projects\\ofc\\client\\src\\app\\not-found":[],"C:\\projects\\ofc\\client\\src\\app\\matches\\page":[]}}
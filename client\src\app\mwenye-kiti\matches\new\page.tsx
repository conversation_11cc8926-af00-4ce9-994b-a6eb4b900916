'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import { useCreateMatch } from '@/lib/hooks'
import { CreateMatchRequest, MatchStatus, MatchResult } from '@/lib/types'

export default function NewMatch() {
  const router = useRouter()
  const createMatch = useCreateMatch()
  
  const [formData, setFormData] = useState<CreateMatchRequest>({
    opponent: '',
    location: '',
    match_date: '',
    match_time: '',
    competition: '',
    result: undefined,
    score_home: undefined,
    score_away: undefined,
    match_status: 'scheduled',
    highlights_url: '',
    image_url: '',
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const matchStatuses: MatchStatus[] = ['scheduled', 'in_progress', 'completed', 'postponed', 'cancelled']
  const matchResults: MatchResult[] = ['win', 'loss', 'draw', 'upcoming']

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.opponent.trim()) {
      newErrors.opponent = 'Opponent is required'
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required'
    }

    if (!formData.match_date) {
      newErrors.match_date = 'Match date is required'
    }

    if (!formData.match_time) {
      newErrors.match_time = 'Match time is required'
    }

    if (formData.score_home !== undefined && formData.score_home < 0) {
      newErrors.score_home = 'Score cannot be negative'
    }

    if (formData.score_away !== undefined && formData.score_away < 0) {
      newErrors.score_away = 'Score cannot be negative'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    const result = await createMatch.mutate(formData)
    if (result) {
      router.push('/mwenye-kiti/matches')
    }
  }

  const handleInputChange = (field: keyof CreateMatchRequest, value: string | number | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="mb-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Link
            href="/mwenye-kiti/matches"
            className="inline-flex items-center text-sm text-gray-500 hover:text-green-600 mb-4 transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Matches
          </Link>
        </motion.div>
        <motion.h1 
          className="text-3xl font-bold bg-gradient-to-r from-green-600 to-red-600 bg-clip-text text-transparent"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Schedule New Match
        </motion.h1>
        <motion.p 
          className="mt-1 text-sm text-gray-600"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          Fill in the details below to schedule a new match.
        </motion.p>
      </div>

      {/* Form */}
      <motion.div 
        className="bg-white shadow-lg rounded-xl border border-gray-100"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <form onSubmit={handleSubmit} className="space-y-6 p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Opponent */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              <label htmlFor="opponent" className="block text-sm font-medium text-gray-700">
                Opponent *
              </label>
              <input
                type="text"
                id="opponent"
                value={formData.opponent}
                onChange={(e) => handleInputChange('opponent', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.opponent ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="Enter opponent team name"
              />
              {errors.opponent && <p className="mt-1 text-sm text-red-600">{errors.opponent}</p>}
            </motion.div>

            {/* Location */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                Location *
              </label>
              <input
                type="text"
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.location ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="e.g., Home Stadium, Away Ground"
              />
              {errors.location && <p className="mt-1 text-sm text-red-600">{errors.location}</p>}
            </motion.div>

            {/* Match Date */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
            >
              <label htmlFor="match_date" className="block text-sm font-medium text-gray-700">
                Match Date *
              </label>
              <input
                type="date"
                id="match_date"
                value={formData.match_date}
                onChange={(e) => handleInputChange('match_date', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.match_date ? 'border-red-300 ring-red-500' : ''
                }`}
              />
              {errors.match_date && <p className="mt-1 text-sm text-red-600">{errors.match_date}</p>}
            </motion.div>

            {/* Match Time */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.7 }}
            >
              <label htmlFor="match_time" className="block text-sm font-medium text-gray-700">
                Match Time *
              </label>
              <input
                type="time"
                id="match_time"
                value={formData.match_time}
                onChange={(e) => handleInputChange('match_time', e.target.value)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.match_time ? 'border-red-300 ring-red-500' : ''
                }`}
              />
              {errors.match_time && <p className="mt-1 text-sm text-red-600">{errors.match_time}</p>}
            </motion.div>

            {/* Competition */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.8 }}
            >
              <label htmlFor="competition" className="block text-sm font-medium text-gray-700">
                Competition
              </label>
              <input
                type="text"
                id="competition"
                value={formData.competition || ''}
                onChange={(e) => handleInputChange('competition', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="e.g., Premier League, Cup Final"
              />
            </motion.div>

            {/* Match Status */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.9 }}
            >
              <label htmlFor="match_status" className="block text-sm font-medium text-gray-700">
                Match Status
              </label>
              <select
                id="match_status"
                value={formData.match_status}
                onChange={(e) => handleInputChange('match_status', e.target.value as MatchStatus)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              >
                {matchStatuses.map(status => (
                  <option key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                  </option>
                ))}
              </select>
            </motion.div>

            {/* Result */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 1.0 }}
            >
              <label htmlFor="result" className="block text-sm font-medium text-gray-700">
                Result
              </label>
              <select
                id="result"
                value={formData.result || ''}
                onChange={(e) => handleInputChange('result', e.target.value as MatchResult || undefined)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              >
                <option value="">Select result</option>
                {matchResults.map(result => (
                  <option key={result} value={result}>
                    {result.charAt(0).toUpperCase() + result.slice(1)}
                  </option>
                ))}
              </select>
            </motion.div>

            {/* Home Score */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 1.1 }}
            >
              <label htmlFor="score_home" className="block text-sm font-medium text-gray-700">
                Home Score
              </label>
              <input
                type="number"
                id="score_home"
                min="0"
                value={formData.score_home || ''}
                onChange={(e) => handleInputChange('score_home', e.target.value ? parseInt(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.score_home ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="0"
              />
              {errors.score_home && <p className="mt-1 text-sm text-red-600">{errors.score_home}</p>}
            </motion.div>

            {/* Away Score */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 1.2 }}
            >
              <label htmlFor="score_away" className="block text-sm font-medium text-gray-700">
                Away Score
              </label>
              <input
                type="number"
                id="score_away"
                min="0"
                value={formData.score_away || ''}
                onChange={(e) => handleInputChange('score_away', e.target.value ? parseInt(e.target.value) : undefined)}
                className={`mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 ${
                  errors.score_away ? 'border-red-300 ring-red-500' : ''
                }`}
                placeholder="0"
              />
              {errors.score_away && <p className="mt-1 text-sm text-red-600">{errors.score_away}</p>}
            </motion.div>

            {/* Highlights URL */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 1.3 }}
            >
              <label htmlFor="highlights_url" className="block text-sm font-medium text-gray-700">
                Highlights URL
              </label>
              <input
                type="url"
                id="highlights_url"
                value={formData.highlights_url || ''}
                onChange={(e) => handleInputChange('highlights_url', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="https://youtube.com/watch?v=..."
              />
            </motion.div>

            {/* Image URL */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 1.4 }}
            >
              <label htmlFor="image_url" className="block text-sm font-medium text-gray-700">
                Image URL
              </label>
              <input
                type="url"
                id="image_url"
                value={formData.image_url || ''}
                onChange={(e) => handleInputChange('image_url', e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                placeholder="https://example.com/match-image.jpg"
              />
            </motion.div>
          </div>

          {/* Error Message */}
          {createMatch.error && (
            <motion.div 
              className="rounded-lg bg-red-50 border border-red-200 p-4"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              <div className="text-sm text-red-700">
                Error creating match: {createMatch.error}
              </div>
            </motion.div>
          )}

          {/* Submit Buttons */}
          <motion.div 
            className="flex justify-end space-x-3"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.5 }}
          >
            <Link
              href="/mwenye-kiti/matches"
              className="bg-white py-2 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
            >
              Cancel
            </Link>
            <motion.button
              type="submit"
              disabled={createMatch.loading}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 transition-all duration-200"
            >
              {createMatch.loading ? 'Creating...' : 'Schedule Match'}
            </motion.button>
          </motion.div>
        </form>
      </motion.div>
    </motion.div>
  )
}

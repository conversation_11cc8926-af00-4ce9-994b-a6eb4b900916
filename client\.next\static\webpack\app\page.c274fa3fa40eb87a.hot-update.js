"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: function() { return /* binding */ capitalizeFirst; },\n/* harmony export */   cn: function() { return /* binding */ cn; },\n/* harmony export */   debounce: function() { return /* binding */ debounce; },\n/* harmony export */   formatDate: function() { return /* binding */ formatDate; },\n/* harmony export */   formatDateTime: function() { return /* binding */ formatDateTime; },\n/* harmony export */   formatFormDate: function() { return /* binding */ formatFormDate; },\n/* harmony export */   formatFormTime: function() { return /* binding */ formatFormTime; },\n/* harmony export */   formatTime: function() { return /* binding */ formatTime; },\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   getFromStorage: function() { return /* binding */ getFromStorage; },\n/* harmony export */   getImageUrl: function() { return /* binding */ getImageUrl; },\n/* harmony export */   getLoadingMessage: function() { return /* binding */ getLoadingMessage; },\n/* harmony export */   getMatchResultColor: function() { return /* binding */ getMatchResultColor; },\n/* harmony export */   getMatchResultText: function() { return /* binding */ getMatchResultText; },\n/* harmony export */   getMatchStatusColor: function() { return /* binding */ getMatchStatusColor; },\n/* harmony export */   getMatchStatusText: function() { return /* binding */ getMatchStatusText; },\n/* harmony export */   getSuccessMessage: function() { return /* binding */ getSuccessMessage; },\n/* harmony export */   groupBy: function() { return /* binding */ groupBy; },\n/* harmony export */   handleApiError: function() { return /* binding */ handleApiError; },\n/* harmony export */   isNetworkError: function() { return /* binding */ isNetworkError; },\n/* harmony export */   isValidEmail: function() { return /* binding */ isValidEmail; },\n/* harmony export */   isValidPhone: function() { return /* binding */ isValidPhone; },\n/* harmony export */   parseFormDate: function() { return /* binding */ parseFormDate; },\n/* harmony export */   removeFromStorage: function() { return /* binding */ removeFromStorage; },\n/* harmony export */   retryOperation: function() { return /* binding */ retryOperation; },\n/* harmony export */   setToStorage: function() { return /* binding */ setToStorage; },\n/* harmony export */   slugify: function() { return /* binding */ slugify; },\n/* harmony export */   sortBy: function() { return /* binding */ sortBy; },\n/* harmony export */   truncateText: function() { return /* binding */ truncateText; }\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isValid/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Date formatting utilities\nfunction formatDate(dateString) {\n    let formatStr = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"MMM dd, yyyy\";\n    try {\n        const date = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(dateString);\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(date) ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date, formatStr) : \"Invalid Date\";\n    } catch (e) {\n        return \"Invalid Date\";\n    }\n}\nfunction formatDateTime(dateString) {\n    let formatStr = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"MMM dd, yyyy HH:mm\";\n    try {\n        const date = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(dateString);\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(date) ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date, formatStr) : \"Invalid Date\";\n    } catch (e) {\n        return \"Invalid Date\";\n    }\n}\nfunction formatTime(timeString) {\n    try {\n        // Handle time format from database (HH:mm:ss)\n        const [hours, minutes] = timeString.split(\":\");\n        const date = new Date();\n        date.setHours(parseInt(hours), parseInt(minutes), 0);\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date, \"h:mm a\");\n    } catch (e) {\n        return timeString;\n    }\n}\n// Match utilities\nfunction getMatchResultColor(result) {\n    switch(result){\n        case \"win\":\n            return \"text-green-600 bg-green-100\";\n        case \"loss\":\n            return \"text-red-600 bg-red-100\";\n        case \"draw\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"upcoming\":\n            return \"text-blue-600 bg-blue-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction getMatchStatusColor(status) {\n    switch(status){\n        case \"scheduled\":\n            return \"text-blue-600 bg-blue-100\";\n        case \"in_progress\":\n            return \"text-orange-600 bg-orange-100\";\n        case \"completed\":\n            return \"text-green-600 bg-green-100\";\n        case \"postponed\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"cancelled\":\n            return \"text-red-600 bg-red-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction getMatchResultText(result) {\n    switch(result){\n        case \"win\":\n            return \"Win\";\n        case \"loss\":\n            return \"Loss\";\n        case \"draw\":\n            return \"Draw\";\n        case \"upcoming\":\n            return \"Upcoming\";\n        default:\n            return \"Unknown\";\n    }\n}\nfunction getMatchStatusText(status) {\n    switch(status){\n        case \"scheduled\":\n            return \"Scheduled\";\n        case \"in_progress\":\n            return \"In Progress\";\n        case \"completed\":\n            return \"Completed\";\n        case \"postponed\":\n            return \"Postponed\";\n        case \"cancelled\":\n            return \"Cancelled\";\n        default:\n            return \"Unknown\";\n    }\n}\n// Image utilities\nfunction getImageUrl(imagePath) {\n    if (!imagePath) return \"/images/placeholder.jpg\";\n    // If it's already a full URL, return as is\n    if (imagePath.startsWith(\"http\")) return imagePath;\n    // If it starts with /uploads, prepend the API base URL\n    if (imagePath.startsWith(\"/uploads\")) {\n        const apiUrl = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8080\";\n        return \"\".concat(apiUrl).concat(imagePath);\n    }\n    // Otherwise, assume it's a relative path\n    return imagePath;\n}\n// String utilities\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + \"...\";\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w ]+/g, \"\").replace(/ +/g, \"-\");\n}\n// Validation utilities\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, \"\"));\n}\n// Form utilities\nfunction formatFormDate(date) {\n    return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date, \"yyyy-MM-dd\");\n}\nfunction formatFormTime(date) {\n    return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date, \"HH:mm\");\n}\nfunction parseFormDate(dateString) {\n    try {\n        const date = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(dateString);\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(date) ? date : null;\n    } catch (e) {\n        return null;\n    }\n}\n// Array utilities\nfunction groupBy(array, key) {\n    return array.reduce((groups, item)=>{\n        const group = String(item[key]);\n        groups[group] = groups[group] || [];\n        groups[group].push(item);\n        return groups;\n    }, {});\n}\nfunction sortBy(array, key) {\n    let order = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"asc\";\n    return [\n        ...array\n    ].sort((a, b)=>{\n        const aVal = a[key];\n        const bVal = b[key];\n        if (aVal < bVal) return order === \"asc\" ? -1 : 1;\n        if (aVal > bVal) return order === \"asc\" ? 1 : -1;\n        return 0;\n    });\n}\n// Loading and error utilities\nfunction getLoadingMessage(entity) {\n    return \"Loading \".concat(entity, \"...\");\n}\nfunction getErrorMessage(entity) {\n    let action = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"load\";\n    return \"Failed to \".concat(action, \" \").concat(entity, \". Please try again.\");\n}\nfunction getSuccessMessage(entity, action) {\n    return \"\".concat(capitalizeFirst(entity), \" \").concat(action, \" successfully!\");\n}\n// Local storage utilities\nfunction getFromStorage(key, defaultValue) {\n    if (false) {}\n    try {\n        const item = localStorage.getItem(key);\n        return item ? JSON.parse(item) : defaultValue;\n    } catch (e) {\n        return defaultValue;\n    }\n}\nfunction setToStorage(key, value) {\n    if (false) {}\n    try {\n        localStorage.setItem(key, JSON.stringify(value));\n    } catch (e) {\n    // Silently fail\n    }\n}\nfunction removeFromStorage(key) {\n    if (false) {}\n    try {\n        localStorage.removeItem(key);\n    } catch (e) {\n    // Silently fail\n    }\n}\n// Debounce utility\nfunction debounce(func, wait) {\n    let timeout;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n// Error handling utilities\nconst handleApiError = (error)=>{\n    var _error_response_data, _error_response;\n    if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n        return error.response.data.message;\n    }\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    return \"An unexpected error occurred\";\n};\nconst isNetworkError = (error)=>{\n    var _error_message;\n    return (error === null || error === void 0 ? void 0 : error.code) === \"NETWORK_ERROR\" || (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"Network Error\"));\n};\nconst retryOperation = async function(operation) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await operation();\n        } catch (error) {\n            lastError = error;\n            if (i === maxRetries) break;\n            // Only retry on network errors\n            if (!isNetworkError(error)) break;\n            // Wait before retrying\n            await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n        }\n    }\n    throw lastError;\n};\n// Class name utility\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return twMerge(clsx(inputs));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});
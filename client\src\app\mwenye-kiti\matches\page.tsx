'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, Edit, Trash2, Search, Calendar, MapPin, Trophy } from 'lucide-react'
import { useMatches, useDeleteMatch } from '@/lib/hooks'
import { formatDate, formatTime, getMatchResultColor, getMatchResultText, getMatchStatusColor, getMatchStatusText } from '@/lib/utils'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  }
}

export default function MatchesAdmin() {
  const { data: matches, loading, error, refetch } = useMatches()
  const deleteMatch = useDeleteMatch()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [deleteConfirm, setDeleteConfirm] = useState<number | null>(null)

  // Filter matches based on search and status
  const filteredMatches = matches?.filter(match => {
    const matchesSearch = match.opponent.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         match.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (match.competition && match.competition.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = !selectedStatus || match.match_status === selectedStatus
    return matchesSearch && matchesStatus
  }) || []

  // Get unique statuses for filter
  const statuses = Array.from(new Set(matches?.map(m => m.match_status) || []))

  const handleDelete = async (id: number) => {
    const result = await deleteMatch.mutate(id)
    if (result) {
      refetch()
      setDeleteConfirm(null)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="rounded-full h-12 w-12 border-4 border-green-200 border-t-green-600"
        />
      </div>
    )
  }

  if (error) {
    return (
      <motion.div 
        className="text-center py-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <p className="text-red-600">Error loading matches: {error}</p>
        <button
          onClick={() => refetch()}
          className="mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
        >
          Try Again
        </button>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Header */}
      <motion.div 
        className="sm:flex sm:items-center sm:justify-between mb-8"
        variants={itemVariants}
      >
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-red-600 bg-clip-text text-transparent">
            Matches
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your team's matches and fixtures
          </p>
        </div>
        <motion.div 
          className="mt-4 sm:mt-0"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Link
            href="/mwenye-kiti/matches/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
          >
            <Plus className="-ml-1 mr-2 h-5 w-5" />
            Schedule Match
          </Link>
        </motion.div>
      </motion.div>

      {/* Filters */}
      <motion.div 
        className="bg-white p-6 rounded-xl shadow-lg mb-6 border border-gray-100"
        variants={itemVariants}
      >
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search matches..."
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <select
            className="block w-full px-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="">All Statuses</option>
            {statuses.map(status => (
              <option key={status} value={status}>{getMatchStatusText(status)}</option>
            ))}
          </select>
        </div>
      </motion.div>

      {/* Matches List */}
      <motion.div 
        className="space-y-4"
        variants={containerVariants}
      >
        <AnimatePresence>
          {filteredMatches.map((match, index) => (
            <motion.div
              key={match.id}
              variants={itemVariants}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              whileHover={{ y: -2, scale: 1.01 }}
              className="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 hover:shadow-xl transition-all duration-300"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <Trophy className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        vs {match.opponent}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {match.competition || 'Match'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMatchStatusColor(match.match_status)}`}>
                      {getMatchStatusText(match.match_status)}
                    </span>
                    {match.result && match.result !== 'upcoming' && (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMatchResultColor(match.result)}`}>
                        {getMatchResultText(match.result)}
                      </span>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2 text-green-500" />
                    {formatDate(match.match_date)} at {formatTime(match.match_time)}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="h-4 w-4 mr-2 text-green-500" />
                    {match.location}
                  </div>
                  {(match.score_home !== null && match.score_away !== null) && (
                    <div className="text-sm font-semibold text-gray-900">
                      Score: {match.score_home} - {match.score_away}
                    </div>
                  )}
                </div>

                <div className="flex justify-end space-x-2">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href={`/mwenye-kiti/matches/${match.id}/edit`}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Link>
                  </motion.div>
                  <motion.button
                    onClick={() => setDeleteConfirm(match.id)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                  >
                    <Trash2 className="h-4 w-4" />
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {filteredMatches.length === 0 && (
        <motion.div 
          className="text-center py-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <p className="text-gray-500">
            {searchTerm || selectedStatus ? 'No matches match your filters.' : 'No matches found.'}
          </p>
          {!searchTerm && !selectedStatus && (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="/mwenye-kiti/matches/new"
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 transition-all duration-200"
              >
                <Plus className="-ml-1 mr-2 h-5 w-5" />
                Schedule First Match
              </Link>
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {deleteConfirm && (
          <motion.div 
            className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div 
              className="relative p-5 border w-96 shadow-lg rounded-xl bg-white"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
            >
              <div className="mt-3 text-center">
                <h3 className="text-lg font-medium text-gray-900">Delete Match</h3>
                <div className="mt-2 px-7 py-3">
                  <p className="text-sm text-gray-500">
                    Are you sure you want to delete this match? This action cannot be undone.
                  </p>
                </div>
                <div className="flex justify-center space-x-4 mt-4">
                  <motion.button
                    onClick={() => setDeleteConfirm(null)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-lg hover:bg-gray-400 transition-colors duration-200"
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    onClick={() => handleDelete(deleteConfirm)}
                    disabled={deleteMatch.loading}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors duration-200"
                  >
                    {deleteMatch.loading ? 'Deleting...' : 'Delete'}
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Toast */}
      <AnimatePresence>
        {deleteMatch.error && (
          <motion.div 
            className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg z-50 shadow-lg"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
          >
            <p>Error deleting match: {deleteMatch.error}</p>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

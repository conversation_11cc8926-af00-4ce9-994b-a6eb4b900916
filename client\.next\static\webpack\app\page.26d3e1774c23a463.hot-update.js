"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!********************************************!*\
  !*** ./src/components/home/<USER>
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LatestNews; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_hooks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/hooks */ \"(app-pages-browser)/./src/lib/hooks.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Animation variants\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 20\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.5\n        }\n    }\n};\nfunction LatestNews() {\n    _s();\n    const { data: news, loading, error } = (0,_lib_hooks__WEBPACK_IMPORTED_MODULE_3__.useNews)();\n    // Get latest 4 news articles\n    const latestNews = (news === null || news === void 0 ? void 0 : news.slice(0, 4)) || [];\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section py-20 bg-gradient-to-b from-white to-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600\",\n                            children: \"Loading latest news...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section py-20 bg-gradient-to-b from-white to-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: [\n                            \"Error loading news: \",\n                            error\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    // No news state\n    if (latestNews.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"section py-20 bg-gradient-to-b from-white to-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: \"Latest News\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No news articles available at the moment.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section py-20 bg-gradient-to-b from-white to-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true,\n                        margin: \"-100px\"\n                    },\n                    transition: {\n                        duration: 0.6\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4\",\n                            children: \"Latest News\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-20 h-1 bg-green-500 mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg max-w-2xl mx-auto\",\n                            children: \"Stay updated with the latest happenings at our club\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true,\n                        margin: \"-100px\"\n                    },\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: latestNews.map((article, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: itemVariants,\n                            whileHover: {\n                                y: -10\n                            },\n                            className: \"bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-56 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getImageUrl)(article.image_url) || \"https://placehold.co/800x600/0284c7/FFFFFF/png?text=News\",\n                                            alt: article.title,\n                                            fill: true,\n                                            className: \"object-cover group-hover:scale-105 transition-transform duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-full\",\n                                            children: article.category || \"News\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-500 text-sm mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatDate)(article.date)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-xl mb-3 group-hover:text-green-600 transition-colors line-clamp-2\",\n                                            children: article.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-5 line-clamp-3\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.truncateText)(article.content, 150)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/news/\".concat(article.id),\n                                            className: \"inline-flex items-center font-semibold text-green-600 hover:text-green-800 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"border-b border-green-600 hover:border-green-800\",\n                                                    children: \"Read More\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M14 5l7 7m0 0l-7 7m7-7H3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, article.id, true, {\n                            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        href: \"/news\",\n                        className: \"btn-primary rounded-full px-8 py-3 shadow-lg hover:shadow-green-500/30 transition-all\",\n                        children: \"View All News\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\projects\\\\ofc\\\\client\\\\src\\\\components\\\\home\\\\LatestNews.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(LatestNews, \"iluCw+5cuyyXM9QUmJkLYqIMaUQ=\", false, function() {\n    return [\n        _lib_hooks__WEBPACK_IMPORTED_MODULE_3__.useNews\n    ];\n});\n_c = LatestNews;\nvar _c;\n$RefreshReg$(_c, \"LatestNews\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});
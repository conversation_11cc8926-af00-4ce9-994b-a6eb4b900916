'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion, useScroll, useTransform } from 'framer-motion'
import { useRef, useEffect, useState } from 'react'

export default function HeroSection() {
  const heroRef = useRef<HTMLDivElement>(null)
  const { scrollY } = useScroll()
  const [isMobile, setIsMobile] = useState(false)
  
  // Parallax effect for background image
  const y = useTransform(scrollY, [0, 500], [0, 150])
  const opacity = useTransform(scrollY, [0, 300], [1, 0.3])
  
  // Check if device is mobile for performance optimization
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Text animation variants
  const titleVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.8, 
        ease: [0.25, 0.1, 0.25, 1.0],
        staggerChildren: 0.1
      }
    }
  }
  
  const wordVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 }
    }
  }
  
  const words = ['Passion.', 'Dedication.', 'Excellence.']

  return (
    <div ref={heroRef} className="relative bg-gradient-to-br from-green-900 via-black to-red-900 overflow-hidden h-[90vh] min-h-[600px] flex items-center">
      {/* Background image with parallax effect */}
      <div className="absolute inset-0">
        <motion.div style={{ y: isMobile ? 0 : y }} className="h-[110%] w-full absolute">
          <Image
            src="https://placehold.co/1920x1080/16a34a/FFFFFF/png?text=Football+Stadium"
            alt="Optiven Football Club"
            fill
            priority
            sizes="100vw"
            quality={90}
            className="object-cover"
            style={{
              objectPosition: 'center center',
              willChange: 'transform'
            }}
          />
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.8 }}
          transition={{ duration: 1 }}
          style={{ opacity: isMobile ? 0.8 : opacity }}
          className="absolute inset-0 bg-gradient-to-r from-green-900/90 via-black/80 to-red-900/70"
        ></motion.div>
      </div>
      
      {/* Hero content */}
      <div className="relative container-custom z-10">
        <motion.div 
          className="max-w-3xl"
          initial="hidden"
          animate="visible"
          variants={titleVariants}
        >
          <div className="overflow-hidden mb-6">
            <motion.div className="flex flex-wrap">
              {words.map((word, index) => (
                <motion.span
                  key={word}
                  custom={index}
                  variants={{
                    hidden: { opacity: 0, y: 100 },
                    visible: (i) => ({
                      opacity: 1,
                      y: 0,
                      transition: {
                        delay: i * 0.2,
                        duration: 0.8,
                        ease: [0.25, 0.1, 0.25, 1.0]
                      }
                    })
                  }}
                  className={`text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold leading-tight ${
                    index === 1 ? 'text-primary-400' : 'text-white'
                  } mr-3`}
                >
                  {word}
                </motion.span>
              ))}
            </motion.div>
          </div>
          
          <motion.p 
            variants={wordVariants}
            className="text-lg sm:text-xl md:text-2xl text-gray-200 mb-10"
          >
            Join our community of football enthusiasts and be part of something special.
            We're more than just a club - we're a family.
          </motion.p>
          
          <motion.div 
            variants={wordVariants}
            className="flex flex-wrap gap-4"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Link href="/matches" className="btn-primary text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-full shadow-lg hover:shadow-green-500/50 transition-all inline-block">
                View Upcoming Matches
              </Link>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Link href="/team" className="glass text-white border border-white/20 hover:bg-white/20 font-semibold py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all inline-block">
                Meet Our Team
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
      
      {/* Animated scroll indicator */}
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2, duration: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut" }}
          className="flex flex-col items-center cursor-pointer"
          onClick={() => {
            const nextSection = heroRef.current?.nextElementSibling
            nextSection?.scrollIntoView({ behavior: 'smooth' })
          }}
        >
          <span className="text-white/70 text-sm mb-2">Scroll Down</span>
          <motion.svg 
            className="w-6 h-6 text-white/70" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
            animate={{ y: [0, 5, 0] }}
            transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut" }}
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </motion.svg>
        </motion.div>
      </motion.div>
      
      {/* Decorative elements */}
      <motion.div
        className="absolute top-1/4 right-10 w-20 h-20 rounded-full bg-green-500/20 blur-xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.5, 0.8, 0.5]
        }}
        transition={{
          repeat: Infinity,
          duration: 8,
          ease: "easeInOut"
        }}
      />

      <motion.div
        className="absolute bottom-1/3 left-10 w-32 h-32 rounded-full bg-red-500/20 blur-xl"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{
          repeat: Infinity,
          duration: 10,
          ease: "easeInOut",
          delay: 1
        }}
      />
    </div>
  )
}
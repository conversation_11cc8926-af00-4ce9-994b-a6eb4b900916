# Football Club Admin System Documentation

## Overview

The Football Club Admin System is a comprehensive content management system built with Next.js, TypeScript, and modern web technologies. It provides a secure, user-friendly interface for managing all aspects of a football club's digital presence.

## Features

### 🔐 Authentication & Security
- Secure admin login system
- Route protection for admin areas
- Session management
- Protected routes at `/mwenye-kiti/*`

### 👥 Player Management
- Complete CRUD operations for players
- Player profiles with photos, stats, and bio
- Position-based filtering and search
- Jersey number management
- Player statistics tracking

### ⚽ Match Management
- Schedule and manage matches
- Track match results and scores
- Competition categorization
- Match status tracking (scheduled, in_progress, completed, etc.)
- Highlights and media management

### 📰 News Management
- Create and publish news articles
- Category-based organization
- Rich text content support
- Featured images
- Author attribution
- Publication date management

### 📸 Gallery Management
- Photo upload and management
- Category-based organization
- Image preview and lightbox
- Bulk operations
- Responsive image handling

### 🏢 Team Management
- Staff and management profiles
- Role-based organization
- Department categorization
- Contact information management
- Organizational hierarchy

### 🎯 Coaching Staff
- Coach profiles and specialties
- Experience tracking
- Achievement records
- Coaching certifications
- Performance metrics

## Technology Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Lucide React** - Icon library
- **React Hooks** - State management

### Styling & Design
- **Green, White, Black, Red** color scheme
- **Responsive design** for all devices
- **Modern animations** inspired by contemporary web design
- **Accessibility-first** approach
- **Mobile-optimized** interface

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Backend API server running on port 8080

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd client
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Configure the following variables:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8080/api
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Access the admin panel**
   - Navigate to `http://localhost:3000/mwenye-kiti/login`
   - Use demo credentials: `admin` / `password`

## Admin System Usage

### Accessing the Admin Panel

1. **Login**
   - URL: `/mwenye-kiti/login`
   - Username: `admin`
   - Password: `password`

2. **Dashboard**
   - Overview of all system statistics
   - Quick action buttons
   - Recent activity summaries

### Managing Content

#### Players
- **Add Player**: Complete form with personal details, position, stats
- **Edit Player**: Update any player information
- **Delete Player**: Remove players with confirmation
- **Search & Filter**: Find players by name, position, nationality

#### Matches
- **Schedule Match**: Set opponent, date, time, location
- **Update Results**: Add scores and match status
- **Manage Competition**: Categorize by league/tournament
- **Track Status**: Monitor match progression

#### News
- **Create Article**: Rich text editor with categories
- **Manage Content**: Edit, update, or remove articles
- **Media Management**: Add featured images
- **Publication Control**: Schedule or publish immediately

#### Gallery
- **Upload Photos**: Add images with descriptions
- **Organize Content**: Category-based organization
- **Preview Images**: Lightbox viewing experience
- **Bulk Operations**: Manage multiple images

#### Team & Coaches
- **Staff Profiles**: Complete team member information
- **Role Management**: Assign positions and departments
- **Contact Details**: Email, phone, and other information
- **Achievement Tracking**: Record accomplishments

## API Integration

### Endpoints
All API calls are made to the backend server at `http://localhost:8080/api`

#### Players
- `GET /players` - Fetch all players
- `POST /players` - Create new player
- `GET /players/:id` - Get specific player
- `PUT /players/:id` - Update player
- `DELETE /players/:id` - Delete player

#### Matches
- `GET /matches` - Fetch all matches
- `POST /matches` - Create new match
- `PUT /matches/:id` - Update match
- `DELETE /matches/:id` - Delete match

#### News
- `GET /news` - Fetch all news articles
- `POST /news` - Create new article
- `PUT /news/:id` - Update article
- `DELETE /news/:id` - Delete article

#### Gallery
- `GET /gallery` - Fetch all gallery images
- `POST /gallery` - Upload new image
- `PUT /gallery/:id` - Update image details
- `DELETE /gallery/:id` - Delete image

#### Team
- `GET /team` - Fetch all team members
- `POST /team` - Add new team member
- `PUT /team/:id` - Update team member
- `DELETE /team/:id` - Remove team member

#### Coaches
- `GET /coaches` - Fetch all coaches
- `POST /coaches` - Add new coach
- `PUT /coaches/:id` - Update coach
- `DELETE /coaches/:id` - Remove coach

### Error Handling
- Comprehensive error boundaries
- Network error detection and retry
- User-friendly error messages
- Loading states for all operations
- Toast notifications for feedback

## Customization

### Color Scheme
The system uses a football club-inspired color palette:
- **Primary Green**: `#16a34a` (success, primary actions)
- **Secondary Red**: `#dc2626` (errors, secondary actions)
- **Accent Black**: `#000000` (text, contrast)
- **Background White**: `#ffffff` (backgrounds, cards)

### Animations
Modern animations powered by Framer Motion:
- Page transitions
- Hover effects
- Loading states
- Form interactions
- Card animations

### Responsive Design
- Mobile-first approach
- Tablet optimization
- Desktop enhancement
- Touch-friendly interactions

## Security Considerations

### Authentication
- Session-based authentication
- Protected route middleware
- Automatic logout on session expiry
- Secure credential handling

### Data Validation
- Client-side form validation
- Server-side validation (backend)
- Input sanitization
- Type safety with TypeScript

### Access Control
- Admin-only routes
- Role-based permissions (future enhancement)
- Secure API endpoints
- CSRF protection (backend)

## Performance Optimization

### Frontend
- Next.js App Router for optimal performance
- Image optimization with Next.js Image component
- Code splitting and lazy loading
- Efficient state management
- Optimized animations with Framer Motion

### API
- Efficient data fetching
- Error retry mechanisms
- Loading state management
- Caching strategies (future enhancement)

## Troubleshooting

### Common Issues

1. **Login Issues**
   - Verify credentials: `admin` / `password`
   - Check backend server is running
   - Clear browser cache and cookies

2. **API Connection Problems**
   - Ensure backend server is running on port 8080
   - Check CORS configuration
   - Verify API endpoint URLs

3. **Image Loading Issues**
   - Check image URL validity
   - Verify CORS headers for external images
   - Ensure proper image formats (jpg, png, webp)

4. **Performance Issues**
   - Check network connection
   - Monitor browser console for errors
   - Verify backend response times

### Debug Mode
Enable development mode for detailed error information:
```bash
npm run dev
```

## Future Enhancements

### Planned Features
- [ ] Role-based access control
- [ ] Advanced analytics dashboard
- [ ] Bulk import/export functionality
- [ ] Advanced search and filtering
- [ ] Real-time notifications
- [ ] Multi-language support
- [ ] Advanced media management
- [ ] Integration with social media
- [ ] Mobile app companion
- [ ] Advanced reporting tools

### Technical Improvements
- [ ] Unit and integration tests
- [ ] End-to-end testing
- [ ] Performance monitoring
- [ ] SEO optimization
- [ ] PWA capabilities
- [ ] Offline functionality
- [ ] Advanced caching
- [ ] Database optimization
- [ ] API rate limiting
- [ ] Enhanced security features

## Support

For technical support or questions:
1. Check this documentation
2. Review the troubleshooting section
3. Check the browser console for errors
4. Verify backend server status
5. Contact the development team

## License

This project is proprietary software developed for the football club's internal use.
